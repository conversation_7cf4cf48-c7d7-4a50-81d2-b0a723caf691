# Enhanced Error Handling System Guide

## Overview

The Vierla Frontend v1 application now includes a comprehensive error handling system that provides:

- **Graceful error recovery** with automatic retry mechanisms
- **User-friendly feedback** with contextual error messages
- **Comprehensive error boundaries** to prevent app crashes
- **Analytics and monitoring** for error tracking
- **Offline support** with intelligent fallback strategies

## Components

### 1. Enhanced Error Boundary (`EnhancedErrorBoundary`)

A React error boundary that catches JavaScript errors anywhere in the component tree and provides recovery mechanisms.

```tsx
import { EnhancedErrorBoundary } from '../components/error/EnhancedErrorBoundary';

<EnhancedErrorBoundary
  screenName="Customer Home"
  enableRetry={true}
  maxRetries={3}
  enableReporting={true}
  criticalError={false}
  onError={(error, errorInfo) => {
    // Custom error handling
  }}
>
  <YourComponent />
</EnhancedErrorBoundary>
```

**Props:**
- `screenName`: Name of the screen for error reporting
- `enableRetry`: Allow users to retry after errors
- `maxRetries`: Maximum number of retry attempts
- `enableReporting`: Enable error reporting to analytics
- `criticalError`: Mark as critical error (affects UI styling)
- `onError`: Custom error handler callback

### 2. Error Display Component (`ErrorDisplay`)

A flexible component for displaying errors with various styles and actions.

```tsx
import { ErrorDisplay } from '../components/error/ErrorDisplay';

<ErrorDisplay
  error="Network connection failed"
  title="Connection Error"
  description="Please check your internet connection and try again."
  actionLabel="Retry"
  onAction={handleRetry}
  variant="card"
  severity="error"
  dismissible={true}
  onDismiss={handleDismiss}
/>
```

**Variants:**
- `inline`: Small inline error display
- `card`: Card-style error display (default)
- `banner`: Full-width banner
- `modal`: Modal-style display
- `fullscreen`: Full-screen error display

**Severity Levels:**
- `error`: Red styling for errors
- `warning`: Orange styling for warnings
- `info`: Blue styling for informational messages

### 3. Enhanced Loading States (`EnhancedLoadingState`)

Comprehensive loading indicators with progress tracking and multiple variants.

```tsx
import { EnhancedLoadingState } from '../components/loading/EnhancedLoadingStates';

<EnhancedLoadingState
  variant="progress"
  message="Loading your data..."
  progress={75}
  showProgress={true}
  steps={['Connecting', 'Authenticating', 'Loading data']}
  currentStep={1}
/>
```

**Variants:**
- `spinner`: Traditional spinning indicator
- `dots`: Animated dots
- `skeleton`: Skeleton loading placeholders
- `progress`: Progress bar with percentage
- `pulse`: Pulsing animation

### 4. Enhanced Empty State (`EnhancedEmptyState`)

Contextual empty state displays with actions and recommendations.

```tsx
import { EnhancedEmptyState } from '../components/empty/EnhancedEmptyState';

<EnhancedEmptyState
  title="No bookings found"
  description="You haven't made any bookings yet. Start by browsing our services."
  icon="calendar-outline"
  primaryAction={{
    label: "Browse Services",
    onPress: () => navigate('Search'),
    variant: 'primary'
  }}
  secondaryAction={{
    label: "Refresh",
    onPress: handleRefresh,
    variant: 'outline'
  }}
  variant="search"
/>
```

### 5. Enhanced User Feedback (`EnhancedUserFeedback`)

Advanced notification system with swipe-to-dismiss, progress tracking, and undo functionality.

```tsx
import { EnhancedUserFeedback } from '../components/feedback/EnhancedUserFeedback';

const [messages, setMessages] = useState([
  {
    id: '1',
    type: 'success',
    message: 'Booking confirmed successfully!',
    duration: 3000,
    undoable: true,
    onUndo: handleUndo,
    actions: [
      {
        label: 'View Details',
        onPress: () => navigate('BookingDetails'),
        style: 'primary'
      }
    ]
  }
]);

<EnhancedUserFeedback
  messages={messages}
  onDismiss={(id) => setMessages(prev => prev.filter(m => m.id !== id))}
  maxVisible={3}
  position="top"
/>
```

## Hooks

### 1. Error Recovery Hook (`useErrorRecovery`)

Provides comprehensive error recovery with automatic retry and network restoration.

```tsx
import { useErrorRecovery } from '../hooks/useErrorRecovery';

const {
  error,
  isError,
  isRecovering,
  retry,
  clearError,
  getErrorMessage,
  isNetworkError,
  getRecoveryRecommendation
} = useErrorRecovery(
  async () => {
    // Your async operation
    return await apiCall();
  },
  {
    maxRetries: 3,
    autoRetryOnNetworkRestore: true,
    autoRetryOnAppFocus: true,
    enableUserNotification: true
  }
);
```

### 2. Enhanced Toast Hook (`useToast`)

Improved toast notifications with type-specific methods.

```tsx
import { useToast } from '../hooks/useToast';

const { showSuccess, showError, showWarning, showInfo } = useToast();

// Type-specific methods
showSuccess('Operation completed successfully!');
showError('Something went wrong. Please try again.');
showWarning('Your session will expire soon.');
showInfo('New features are available.');
```

## Services

### Enhanced Error Handling Service

Centralized error handling with recovery strategies and analytics.

```tsx
import { enhancedErrorHandlingService } from '../services/enhancedErrorHandlingService';

// Handle errors with context
enhancedErrorHandlingService.handleError(error, {
  component: 'CustomerHomeScreen',
  action: 'load_data',
  userId: user.id,
  additionalData: { searchQuery }
});

// Add custom recovery strategies
enhancedErrorHandlingService.addRecoveryStrategy({
  name: 'custom_recovery',
  description: 'Custom recovery for specific errors',
  execute: async () => {
    // Recovery logic
    return true;
  },
  conditions: (error) => error.message.includes('specific_error'),
  priority: 1
});
```

## Best Practices

### 1. Error Boundary Placement

- Place error boundaries at strategic points in your component tree
- Use screen-level boundaries for major sections
- Use component-level boundaries for critical components

### 2. Error Message Guidelines

- **Be specific**: Explain what went wrong and why
- **Be actionable**: Provide clear next steps
- **Be empathetic**: Use friendly, non-technical language
- **Be helpful**: Offer alternatives when possible

### 3. Loading State Management

- Show loading states for operations > 200ms
- Use skeleton loading for content-heavy screens
- Provide progress indicators for long operations
- Include meaningful loading messages

### 4. Empty State Design

- Make empty states discoverable and actionable
- Provide clear guidance on how to populate the screen
- Use appropriate imagery and messaging
- Include relevant actions (refresh, create, browse)

### 5. Error Recovery

- Implement automatic retry for transient errors
- Provide manual retry options for user-initiated actions
- Use exponential backoff for network retries
- Cache data for offline scenarios

## Implementation Checklist

- [ ] Wrap screens with `EnhancedErrorBoundary`
- [ ] Replace basic error displays with `ErrorDisplay`
- [ ] Implement loading states with `EnhancedLoadingState`
- [ ] Add empty states with `EnhancedEmptyState`
- [ ] Integrate user feedback with `EnhancedUserFeedback`
- [ ] Use error recovery hooks for async operations
- [ ] Configure error handling service
- [ ] Add error analytics and monitoring
- [ ] Test error scenarios and recovery flows
- [ ] Document error handling patterns for team

## Testing Error Handling

### Manual Testing

1. **Network Errors**: Disable network and test offline scenarios
2. **Server Errors**: Mock 500 errors and test recovery
3. **Authentication Errors**: Test token expiration scenarios
4. **Validation Errors**: Test form validation and error display
5. **Component Errors**: Introduce intentional errors to test boundaries

### Automated Testing

```tsx
// Test error boundary
it('should display error boundary when component throws', () => {
  const ThrowError = () => {
    throw new Error('Test error');
  };

  render(
    <EnhancedErrorBoundary>
      <ThrowError />
    </EnhancedErrorBoundary>
  );

  expect(screen.getByText('Something went wrong')).toBeInTheDocument();
});

// Test error recovery
it('should retry operation on error', async () => {
  const mockOperation = jest.fn()
    .mockRejectedValueOnce(new Error('Network error'))
    .mockResolvedValueOnce('success');

  const { result } = renderHook(() => useErrorRecovery(mockOperation));

  await act(async () => {
    await result.current.retry();
  });

  expect(mockOperation).toHaveBeenCalledTimes(2);
});
```

This enhanced error handling system provides a robust foundation for handling errors gracefully while maintaining a positive user experience.
