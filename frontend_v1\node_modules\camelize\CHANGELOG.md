# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.1](https://github.com/ljharb/camelize/compare/v1.0.0...v1.0.1) - 2022-10-11

### Commits

- [eslint] fix indentation [`1c978b3`](https://github.com/ljharb/camelize/commit/1c978b349113cef7564e869470ee46a4a0b2ed12)
- [readme] rename, add badges [`640a9c9`](https://github.com/ljharb/camelize/commit/640a9c9509c934fa0af0871cf3a2c57b44e2e17d)
- [actions] add reusable workflows [`0c27439`](https://github.com/ljharb/camelize/commit/0c2743965ceadbf10928b112b2f505bf8bd1d70e)
- [eslint] add eslint [`c2f99b1`](https://github.com/ljharb/camelize/commit/c2f99b1ca92adf2d68077f9c9814cbe5c35283e3)
- [meta] add `auto-changelog` [`18083fe`](https://github.com/ljharb/camelize/commit/18083fed5ac1f60d34371c25c25939829fc359b1)
- [Dev Deps] update `tape` [`f11a870`](https://github.com/ljharb/camelize/commit/f11a870a40077552d28cfaaf85b97c638d90d696)
- [meta] create FUNDING.yml; add `funding` in package.json [`83d0195`](https://github.com/ljharb/camelize/commit/83d0195aef76624fbbf0e75b55878da2897a890e)
- [meta] use `npmignore` to autogenerate an npmignore file [`34862da`](https://github.com/ljharb/camelize/commit/34862dac5a9566f5d30ddcfc73d735875a8fe13a)
- Only apps should have lockfiles [`270fb10`](https://github.com/ljharb/camelize/commit/270fb103d8061b687f705c49f512ff01353e3ca2)
- [meta] update URLs [`fa51c88`](https://github.com/ljharb/camelize/commit/fa51c88806e3347b564dccd8df9dfcd440f523b3)
- [meta] add `safe-publish-latest` [`f141183`](https://github.com/ljharb/camelize/commit/f141183b01229d0a3db085464ecd052dc0dbf331)
- [Tests] add `aud` in `posttest` [`276e8a2`](https://github.com/ljharb/camelize/commit/276e8a2285d1cc37656cf630ec7961fc6efffe16)

## [v1.0.0](https://github.com/ljharb/camelize/compare/v0.2.0...v1.0.0) - 2014-07-07

## [v0.2.0](https://github.com/ljharb/camelize/compare/v0.1.2...v0.2.0) - 2014-07-07

### Commits

- add support for dates and regex [`5c3ae6c`](https://github.com/ljharb/camelize/commit/5c3ae6cb8d5c14fdc47967d194359eced981fb00)

## [v0.1.2](https://github.com/ljharb/camelize/compare/v0.1.1...v0.1.2) - 2013-12-18

### Commits

- shims for ie&lt;9 [`e38b6aa`](https://github.com/ljharb/camelize/commit/e38b6aa03bdd586cd190544515e25d6f9d965a09)

## [v0.1.1](https://github.com/ljharb/camelize/compare/v0.1.0...v0.1.1) - 2013-12-18

### Commits

- failing test for camelizing nested strings [`e4830da`](https://github.com/ljharb/camelize/commit/e4830da24b3f786b1080d726221a48fc708c98ae)
- do not camelcase nested string values [`5adb7c6`](https://github.com/ljharb/camelize/commit/5adb7c6e2d5d41e21f02a59df0bc7eff6ad8890a)

## [v0.1.0](https://github.com/ljharb/camelize/compare/v0.0.0...v0.1.0) - 2013-06-30

### Commits

- camelCase the value if it's a string. [`2322d77`](https://github.com/ljharb/camelize/commit/2322d771557115a95133a5422e07a3f472b65018)
- test for strings [`2bb412c`](https://github.com/ljharb/camelize/commit/2bb412c43485f8e810ce5353628ea00a6fb250de)
- return object [`caa3f44`](https://github.com/ljharb/camelize/commit/caa3f44e053895ae1471b8ba3ab247dda1b88ddb)

## v0.0.0 - 2013-03-22

### Commits

- docs [`055791a`](https://github.com/ljharb/camelize/commit/055791a45b4fefb87ff598b8f6dcda8f34e45be6)
- package.json etc [`859ff9b`](https://github.com/ljharb/camelize/commit/859ff9b475df18a09b9785068ec1730f84081291)
- a passing test [`8987c4b`](https://github.com/ljharb/camelize/commit/8987c4bd9d2ca798551cf01443ddcfe6a3ebf5f9)
- working example [`46e18af`](https://github.com/ljharb/camelize/commit/46e18afcfded7d3a5d525b4765eb488b998db791)
- add testling [`9b3ae79`](https://github.com/ljharb/camelize/commit/9b3ae7979689614dd1b88b4dea7b970d27ff6ce0)
- badges [`f140dfd`](https://github.com/ljharb/camelize/commit/f140dfd33e89493c69a001643bae911a297303e6)
- using travis [`ef4251b`](https://github.com/ljharb/camelize/commit/ef4251bd3b4f86f3207a84bd3527cbfe0f4750d5)
