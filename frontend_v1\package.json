{"name": "frontend_v1", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:clear": "expo start --clear", "start:production": "expo start --no-dev --minify", "android": "expo start --android", "android:release": "expo run:android --variant release", "ios": "expo start --ios", "ios:release": "expo run:ios --configuration Release", "web": "expo start --web", "web:pwa": "expo start --web --https", "build": "expo export", "build:pwa": "expo export --platform web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "tdd": "node scripts/tdd-protocol.js test", "tdd:watch": "node scripts/tdd-protocol.js watch", "tdd:critical": "node scripts/tdd-protocol.js critical", "tdd:coverage": "node scripts/tdd-protocol.js coverage", "tdd:checkpoint": "node scripts/tdd-protocol.js checkpoint", "tdd:revert": "node scripts/tdd-protocol.js revert", "tdd:status": "node scripts/tdd-protocol.js status", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:ci": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 0", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "build:tokens": "style-dictionary build", "analyze:bundle": "node scripts/bundle-analyzer.js", "optimize:bundle": "node scripts/bundle-optimizer.js", "optimize:images": "node scripts/image-optimizer.js", "optimize:performance": "npm run optimize:bundle && npm run optimize:images", "clean": "expo r -c && npm run clean:deps", "clean:deps": "rm -rf node_modules && npm install", "prebuild": "npm run type-check && npm run lint:ci && npm run test:ci && npm run optimize:performance", "postinstall": "expo install --fix"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native/metro-config": "^0.80.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "expo": "53.0.20", "expo-calendar": "~14.1.4", "expo-crypto": "^14.1.5", "expo-document-picker": "^13.1.6", "expo-haptics": "^14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-local-authentication": "^16.0.5", "expo-location": "~18.1.6", "expo-notifications": "^0.31.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.20.1", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.11.2", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "styled-components": "^6.1.19", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native/eslint-config": "^0.80.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.2.0", "jest": "~29.7.0", "jest-environment-jsdom": "^30.0.4", "jest-junit": "^16.0.0", "prettier": "^3.6.2", "react-test-renderer": "^19.0.0", "style-dictionary": "^5.0.1", "typescript": "~5.8.3"}, "private": true}