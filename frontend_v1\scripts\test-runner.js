/**
 * Comprehensive Test Runner Script
 * 
 * Features:
 * - Runs unit, integration, and e2e tests
 * - Generates coverage reports
 * - Performs performance testing
 * - Validates accessibility
 * - Supports CI/CD integration
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Configuration
const config = {
  testCommand: 'npx jest',
  coverageThreshold: 80,
  maxTestDuration: 60000, // 60 seconds
  performanceThreshold: 5000, // 5 seconds
  testTypes: ['unit', 'integration', 'e2e', 'accessibility'],
  coverageDir: './coverage',
  reportDir: './test-results',
  accessibilityThreshold: 90, // 90% accessibility compliance
  performanceThresholds: {
    renderTime: 1000, // 1 second
    memoryUsage: 10000000, // 10MB
    bundleSize: 5000000, // 5MB
  },
};

// Ensure directories exist
[config.coverageDir, config.reportDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Helper functions
const formatTime = (ms) => {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
};

const logSection = (title) => {
  console.log('\n' + chalk.bgBlue.white.bold(` ${title} `) + '\n');
};

const logSuccess = (message) => {
  console.log(chalk.green('✓ ') + message);
};

const logError = (message) => {
  console.log(chalk.red('✗ ') + message);
};

const logWarning = (message) => {
  console.log(chalk.yellow('⚠ ') + message);
};

const logInfo = (message) => {
  console.log(chalk.blue('ℹ ') + message);
};

const runCommand = (command, options = {}) => {
  try {
    const startTime = Date.now();
    const output = execSync(command, {
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf-8',
    });
    const duration = Date.now() - startTime;
    
    return { success: true, output, duration };
  } catch (error) {
    return { 
      success: false, 
      output: error.stdout || error.message,
      error,
    };
  }
};

// Main test runner
const runTests = (type) => {
  logSection(`Running ${type} tests`);
  
  const testCommand = `${config.testCommand} --testMatch="**/*.${type}.test.{ts,tsx,js,jsx}"`;
  const result = runCommand(testCommand);
  
  if (result.success) {
    logSuccess(`${type} tests completed in ${formatTime(result.duration)}`);
    return true;
  } else {
    logError(`${type} tests failed`);
    return false;
  }
};

// Coverage analysis
const analyzeCoverage = () => {
  logSection('Analyzing test coverage');
  
  const coverageCommand = `${config.testCommand} --coverage --coverageReporters=json-summary`;
  const result = runCommand(coverageCommand, { silent: true });
  
  if (!result.success) {
    logError('Failed to generate coverage report');
    return false;
  }
  
  try {
    const coverageSummary = JSON.parse(
      fs.readFileSync(path.join(config.coverageDir, 'coverage-summary.json'), 'utf-8')
    );
    
    const { total } = coverageSummary;
    
    console.log(chalk.bold('\nCoverage Summary:'));
    console.log(`Statements: ${formatCoverage(total.statements.pct)}%`);
    console.log(`Branches:   ${formatCoverage(total.branches.pct)}%`);
    console.log(`Functions:  ${formatCoverage(total.functions.pct)}%`);
    console.log(`Lines:      ${formatCoverage(total.lines.pct)}%`);
    
    const allAboveThreshold = 
      total.statements.pct >= config.coverageThreshold &&
      total.branches.pct >= config.coverageThreshold &&
      total.functions.pct >= config.coverageThreshold &&
      total.lines.pct >= config.coverageThreshold;
    
    if (allAboveThreshold) {
      logSuccess(`All coverage metrics above threshold (${config.coverageThreshold}%)`);
      return true;
    } else {
      logWarning(`Some coverage metrics below threshold (${config.coverageThreshold}%)`);
      return false;
    }
  } catch (error) {
    logError(`Error analyzing coverage: ${error.message}`);
    return false;
  }
};

const formatCoverage = (value) => {
  if (value === undefined) return chalk.gray('N/A');
  
  const formattedValue = value.toFixed(2);
  if (value < config.coverageThreshold) {
    return chalk.red(formattedValue);
  } else if (value < config.coverageThreshold + 10) {
    return chalk.yellow(formattedValue);
  } else {
    return chalk.green(formattedValue);
  }
};

// Performance testing
const runPerformanceTests = () => {
  logSection('Running performance tests');
  
  const perfCommand = `${config.testCommand} --testMatch="**/*.perf.test.{ts,tsx,js,jsx}"`;
  const result = runCommand(perfCommand);
  
  if (!result.success) {
    logError('Performance tests failed');
    return false;
  }
  
  // Check for performance regressions
  try {
    const perfResults = JSON.parse(
      fs.readFileSync(path.join(config.reportDir, 'performance.json'), 'utf-8')
    );
    
    let hasRegressions = false;
    
    Object.entries(perfResults).forEach(([testName, duration]) => {
      if (duration > config.performanceThreshold) {
        logWarning(`Performance regression in ${testName}: ${formatTime(duration)}`);
        hasRegressions = true;
      } else {
        logSuccess(`${testName}: ${formatTime(duration)}`);
      }
    });
    
    return !hasRegressions;
  } catch (error) {
    logWarning(`Could not analyze performance results: ${error.message}`);
    return true; // Don't fail the build for this
  }
};

// Accessibility testing
const runAccessibilityTests = () => {
  logSection('Running accessibility tests');
  
  const a11yCommand = `${config.testCommand} --testMatch="**/*.a11y.test.{ts,tsx,js,jsx}"`;
  const result = runCommand(a11yCommand);
  
  return result.success;
};

// Main execution
const main = () => {
  logSection('Starting test suite');
  
  const startTime = Date.now();
  let allPassed = true;
  
  // Run different test types
  for (const testType of config.testTypes) {
    const passed = runTests(testType);
    allPassed = allPassed && passed;
    
    if (!passed && process.env.CI) {
      logError(`${testType} tests failed in CI environment, aborting`);
      process.exit(1);
    }
  }
  
  // Run coverage analysis
  const coveragePassed = analyzeCoverage();
  allPassed = allPassed && coveragePassed;
  
  // Run performance tests
  const perfPassed = runPerformanceTests();
  allPassed = allPassed && perfPassed;
  
  // Run accessibility tests
  const a11yPassed = runAccessibilityTests();
  allPassed = allPassed && a11yPassed;
  
  const totalDuration = Date.now() - startTime;
  
  logSection('Test Suite Summary');
  console.log(`Total duration: ${formatTime(totalDuration)}`);
  console.log(`Unit tests: ${formatResult(runTests)}`);
  console.log(`Integration tests: ${formatResult(runTests)}`);
  console.log(`E2E tests: ${formatResult(runTests)}`);
  console.log(`Coverage: ${formatResult(coveragePassed)}`);
  console.log(`Performance: ${formatResult(perfPassed)}`);
  console.log(`Accessibility: ${formatResult(a11yPassed)}`);
  
  if (allPassed) {
    logSuccess('All tests passed successfully!');
    process.exit(0);
  } else {
    logError('Some tests failed');
    process.exit(1);
  }
};

const formatResult = (result) => {
  return result ? chalk.green('PASSED') : chalk.red('FAILED');
};

// Enhanced test functions
const runAccessibilityTests = () => {
  logSection('Running accessibility tests');

  const accessibilityCommand = `${config.testCommand} --testPathPattern="accessibility" --coverage`;
  const result = runCommand(accessibilityCommand);

  if (result.success) {
    logSuccess(`Accessibility tests completed in ${formatTime(result.duration)}`);
    return true;
  } else {
    logError('Accessibility tests failed');
    return false;
  }
};

const runIntegrationTests = () => {
  logSection('Running integration tests');

  const integrationCommand = `${config.testCommand} --testPathPattern="integration" --coverage --timeout=120000`;
  const result = runCommand(integrationCommand);

  if (result.success) {
    logSuccess(`Integration tests completed in ${formatTime(result.duration)}`);
    return true;
  } else {
    logError('Integration tests failed');
    return false;
  }
};

const runEnhancedTestSuite = async () => {
  console.log(chalk.blue.bold('🚀 Vierla Frontend Enhanced Test Suite\n'));

  const startTime = Date.now();
  const results = {};

  // Run all test types
  if (config.testTypes.includes('unit')) {
    results.unit = runTests('unit');
  }

  if (config.testTypes.includes('integration')) {
    results.integration = runIntegrationTests();
  }

  if (config.testTypes.includes('accessibility')) {
    results.accessibility = runAccessibilityTests();
  }

  if (config.testTypes.includes('e2e')) {
    results.e2e = runTests('e2e');
  }

  // Run additional validations
  results.coverage = analyzeCoverage();

  const endTime = Date.now();
  const duration = endTime - startTime;

  // Final summary
  logSection('Enhanced Test Suite Summary');
  console.log(`Total duration: ${formatTime(duration)}`);

  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;

  console.log(`Tests passed: ${passed}/${total}`);

  if (passed < total) {
    logError(`${total - passed} test suite(s) failed`);
    process.exit(1);
  } else {
    logSuccess('All test suites passed!');
    process.exit(0);
  }
};

// Execute main function or enhanced suite
if (process.argv.includes('--enhanced')) {
  runEnhancedTestSuite().catch(error => {
    console.error(chalk.red('Enhanced test runner failed:'), error);
    process.exit(1);
  });
} else {
  main();
}
