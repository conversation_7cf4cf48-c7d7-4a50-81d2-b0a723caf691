/**
 * Enhanced Features Integration Test Suite - End-to-End Testing with Aura Design System
 *
 * Test Contract:
 * - Tests integration between enhanced screens and backend services
 * - Validates complete user flows with real-time features and error handling
 * - Tests accessibility compliance across integrated features
 * - Ensures proper state management and performance across the application
 * - Validates WebSocket integration, caching, and offline functionality
 *
 * @version 3.0.0 - Enhanced with Comprehensive Integration Testing
 * <AUTHOR> Development Team
 */

import React from 'react';
import { render, fireEvent, waitFor, screen, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider } from 'react-redux';

// Components under test
import { MessagesScreen } from '../../screens/MessagesScreen';
import { AccountSettingsScreen } from '../../screens/AccountSettingsScreen';
import { CustomerHomeScreen } from '../../screens/CustomerHomeScreen';

// Test utilities and mocks
import { createMockStore, mockNavigationProps } from '../utils/testUtils';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { mockTheme } from '../__mocks__/theme';

// Mock services
jest.mock('../../services/messagingService');
jest.mock('../../services/websocketService');
jest.mock('../../services/cacheService');
jest.mock('../../services/providerService');
jest.mock('../../services/bookingService');

// Mock performance and error handling
jest.mock('../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    trackUserInteraction: jest.fn((name, fn) => fn()),
    measureRenderTime: jest.fn(),
    trackMemoryUsage: jest.fn(),
  }),
}));

jest.mock('../../hooks/useErrorHandling', () => ({
  useErrorHandling: () => ({
    handleError: jest.fn(),
    clearError: jest.fn(),
    isError: false,
    error: null,
  }),
}));

const mockStore = createMockStore({
  auth: {
    user: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      role: 'customer',
    },
    isAuthenticated: true,
    authToken: 'mock-token',
  },
});

const Stack = createStackNavigator();

const TestApp: React.FC = () => (
  <Provider store={mockStore}>
    <ThemeProvider theme={mockTheme}>
      <NavigationContainer>
        <Stack.Navigator initialRouteName="Home">
          <Stack.Screen name="Home" component={CustomerHomeScreen} />
          <Stack.Screen name="Messages" component={MessagesScreen} />
          <Stack.Screen name="AccountSettings" component={AccountSettingsScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    </ThemeProvider>
  </Provider>
);

describe('Enhanced Features Integration', () => {
  const mockMessagingService = require('../../services/messagingService');
  const mockWebSocketService = require('../../services/websocketService');
  const mockCacheService = require('../../services/cacheService');

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockMessagingService.getConversations.mockResolvedValue({
      conversations: [
        {
          id: 'conv-1',
          providerId: 'provider-1',
          providerName: 'John Smith',
          lastMessage: 'Hello, I need help with...',
          timestamp: new Date().toISOString(),
          unreadCount: 2,
          avatar: 'https://example.com/avatar1.jpg',
        },
      ],
      total: 1,
    });

    mockWebSocketService.connect.mockResolvedValue(true);
    mockCacheService.get.mockResolvedValue(null);
    mockCacheService.set.mockResolvedValue(true);
  });

  describe('Cross-Screen Navigation Integration', () => {
    it('navigates from home to messages and maintains state', async () => {
      render(<TestApp />);

      // Start on home screen
      await waitFor(() => {
        expect(screen.getByText('Welcome back, John!')).toBeTruthy();
      });

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Messages')).toBeTruthy();
        expect(screen.getByText('John Smith')).toBeTruthy();
      });

      // Verify WebSocket connection was established
      expect(mockWebSocketService.connect).toHaveBeenCalled();
    });

    it('navigates from messages to account settings', async () => {
      render(<TestApp />);

      // Navigate to messages first
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Messages')).toBeTruthy();
      });

      // Navigate to account settings
      const settingsButton = screen.getByTestId('header-settings-button');
      fireEvent.press(settingsButton);

      await waitFor(() => {
        expect(screen.getByText('Account Settings')).toBeTruthy();
        expect(screen.getByText('John Doe')).toBeTruthy();
      });
    });
  });

  describe('Real-time Features Integration', () => {
    it('handles real-time message updates across screens', async () => {
      let messageHandler: Function;
      mockWebSocketService.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'chat_message') {
          messageHandler = handler;
        }
      });

      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Hello, I need help with...')).toBeTruthy();
      });

      // Simulate real-time message
      act(() => {
        messageHandler({
          conversation_id: 'conv-1',
          content: 'New real-time message',
          created_at: new Date().toISOString(),
        });
      });

      await waitFor(() => {
        expect(screen.getByText('New real-time message')).toBeTruthy();
      });
    });

    it('maintains WebSocket connection across screen transitions', async () => {
      render(<TestApp />);

      // Navigate to messages (establishes WebSocket)
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(mockWebSocketService.connect).toHaveBeenCalled();
      });

      // Navigate to settings
      const settingsButton = screen.getByTestId('header-settings-button');
      fireEvent.press(settingsButton);

      // Navigate back to messages
      fireEvent.press(messagesTab);

      // WebSocket should still be connected (not reconnected)
      expect(mockWebSocketService.connect).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling Integration', () => {
    it('handles network errors gracefully across screens', async () => {
      mockMessagingService.getConversations.mockRejectedValue(new Error('Network error'));

      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Failed to load conversations')).toBeTruthy();
        expect(screen.getByText('Try Again')).toBeTruthy();
      });

      // Test retry functionality
      mockMessagingService.getConversations.mockResolvedValue({
        conversations: [],
        total: 0,
      });

      fireEvent.press(screen.getByText('Try Again'));

      await waitFor(() => {
        expect(screen.getByText('No conversations yet')).toBeTruthy();
      });
    });

    it('handles WebSocket connection failures', async () => {
      mockWebSocketService.connect.mockRejectedValue(new Error('WebSocket connection failed'));

      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        // Should still show conversations even if WebSocket fails
        expect(screen.getByText('Messages')).toBeTruthy();
      });

      // Verify error was handled gracefully
      expect(mockWebSocketService.connect).toHaveBeenCalled();
    });
  });

  describe('Caching Integration', () => {
    it('uses cached data when available', async () => {
      const cachedConversations = [
        {
          id: 'cached-conv-1',
          providerName: 'Cached Provider',
          lastMessage: 'Cached message',
        },
      ];

      mockCacheService.get.mockResolvedValue(cachedConversations);

      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Cached Provider')).toBeTruthy();
      });

      // Verify cache was checked
      expect(mockCacheService.get).toHaveBeenCalledWith('conversations_page_1');
    });

    it('updates cache with fresh data', async () => {
      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });

      // Verify cache was updated
      expect(mockCacheService.set).toHaveBeenCalledWith(
        'conversations_page_1',
        expect.any(Array),
        expect.any(Number)
      );
    });
  });

  describe('Performance Integration', () => {
    it('tracks performance metrics across screens', async () => {
      const mockTrackUserInteraction = jest.fn((name, fn) => fn());
      
      jest.doMock('../../hooks/usePerformance', () => ({
        usePerformance: () => ({
          trackUserInteraction: mockTrackUserInteraction,
          measureRenderTime: jest.fn(),
        }),
      }));

      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Messages')).toBeTruthy();
      });

      // Verify performance tracking
      expect(mockTrackUserInteraction).toHaveBeenCalledWith(
        'screen_navigation',
        expect.any(Function)
      );
    });

    it('handles memory management during screen transitions', async () => {
      const mockTrackMemoryUsage = jest.fn();
      
      jest.doMock('../../hooks/usePerformance', () => ({
        usePerformance: () => ({
          trackUserInteraction: jest.fn((name, fn) => fn()),
          trackMemoryUsage: mockTrackMemoryUsage,
        }),
      }));

      render(<TestApp />);

      // Navigate between screens multiple times
      const messagesTab = screen.getByTestId('tab-messages');
      const homeTab = screen.getByTestId('tab-home');

      for (let i = 0; i < 3; i++) {
        fireEvent.press(messagesTab);
        await waitFor(() => {
          expect(screen.getByText('Messages')).toBeTruthy();
        });

        fireEvent.press(homeTab);
        await waitFor(() => {
          expect(screen.getByText('Welcome back, John!')).toBeTruthy();
        });
      }

      // Verify memory tracking
      expect(mockTrackMemoryUsage).toHaveBeenCalled();
    });
  });

  describe('Accessibility Integration', () => {
    it('maintains accessibility focus across screen transitions', async () => {
      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        const screenReader = screen.getByTestId('messages-screen-reader');
        expect(screenReader.props.announceOnMount).toBe(
          'Messages screen loaded. View and manage your conversations.'
        );
      });

      // Navigate to settings
      const settingsButton = screen.getByTestId('header-settings-button');
      fireEvent.press(settingsButton);

      await waitFor(() => {
        const settingsScreenReader = screen.getByTestId('account-settings-main');
        expect(settingsScreenReader.props.announceOnMount).toBe(
          'Account settings screen loaded. Manage your profile and preferences.'
        );
      });
    });

    it('supports keyboard navigation across screens', async () => {
      render(<TestApp />);

      // Test keyboard navigation on messages screen
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        const conversationItem = screen.getByTestId('conversation-item-conv-1');
        expect(conversationItem.props.accessible).toBe(true);
        expect(conversationItem.props.accessibilityRole).toBe('button');
      });
    });
  });

  describe('State Management Integration', () => {
    it('maintains global state across screen transitions', async () => {
      render(<TestApp />);

      // Verify user state is available on home screen
      await waitFor(() => {
        expect(screen.getByText('Welcome back, John!')).toBeTruthy();
      });

      // Navigate to settings and verify user data
      const settingsButton = screen.getByTestId('header-settings-button');
      fireEvent.press(settingsButton);

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeTruthy();
        expect(screen.getByText('<EMAIL>')).toBeTruthy();
      });
    });

    it('handles authentication state changes', async () => {
      const storeWithLogout = createMockStore({
        auth: {
          user: null,
          isAuthenticated: false,
          authToken: null,
        },
      });

      render(
        <Provider store={storeWithLogout}>
          <ThemeProvider theme={mockTheme}>
            <TestApp />
          </ThemeProvider>
        </Provider>
      );

      // Should handle unauthenticated state gracefully
      await waitFor(() => {
        // Would typically redirect to login or show appropriate UI
        expect(screen.getByTestId('app-container')).toBeTruthy();
      });
    });
  });

  describe('Offline Functionality Integration', () => {
    it('handles offline mode gracefully', async () => {
      // Mock network failure
      mockMessagingService.getConversations.mockRejectedValue(
        new Error('Network request failed')
      );

      render(<TestApp />);

      // Navigate to messages
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Failed to load conversations')).toBeTruthy();
      });

      // Should still allow navigation to other screens
      const homeTab = screen.getByTestId('tab-home');
      fireEvent.press(homeTab);

      await waitFor(() => {
        expect(screen.getByText('Welcome back, John!')).toBeTruthy();
      });
    });

    it('syncs data when connection is restored', async () => {
      // Start with network failure
      mockMessagingService.getConversations.mockRejectedValueOnce(
        new Error('Network request failed')
      );

      render(<TestApp />);

      // Navigate to messages (fails initially)
      const messagesTab = screen.getByTestId('tab-messages');
      fireEvent.press(messagesTab);

      await waitFor(() => {
        expect(screen.getByText('Failed to load conversations')).toBeTruthy();
      });

      // Restore network and retry
      mockMessagingService.getConversations.mockResolvedValue({
        conversations: [
          {
            id: 'conv-1',
            providerName: 'John Smith',
            lastMessage: 'Hello, I need help with...',
          },
        ],
        total: 1,
      });

      fireEvent.press(screen.getByText('Try Again'));

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });
    });
  });
});
