/**
 * Enhanced Account Settings Screen Test Suite - Comprehensive Testing with Aura Design System
 *
 * Test Contract:
 * - Tests AccountSettingsScreen component with backend integration and profile management
 * - Validates settings management, user preferences, and navigation functionality
 * - Tests accessibility compliance, error handling, and user interactions
 * - Ensures proper state management and performance optimization
 * - Validates role switching, payment methods, and comprehensive profile features
 *
 * @version 3.0.0 - Enhanced with Comprehensive Testing for Aura Design System
 * <AUTHOR> Development Team
 */

import React from 'react';
import { render, fireEvent, waitFor, screen, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider } from 'react-redux';

// Component under test
import { AccountSettingsScreen } from '../../screens/AccountSettingsScreen';

// Test utilities and mocks
import { mockNavigationProps, createMockStore } from '../utils/testUtils';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { mockTheme } from '../__mocks__/theme';

// Mock services and hooks
jest.mock('../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    trackUserInteraction: jest.fn((name, fn) => fn()),
    measureRenderTime: jest.fn(),
    trackMemoryUsage: jest.fn(),
  }),
}));

jest.mock('../../hooks/useErrorHandling', () => ({
  useErrorHandling: () => ({
    handleError: jest.fn(),
    clearError: jest.fn(),
    isError: false,
    error: null,
  }),
}));

jest.mock('../../services/cacheService', () => ({
  clear: jest.fn(),
  get: jest.fn(),
  set: jest.fn(),
}));

// Mock store with user data
const mockStore = createMockStore({
  auth: {
    user: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg',
      role: 'customer',
    },
    isAuthenticated: true,
    logout: jest.fn(),
  },
});

const Stack = createStackNavigator();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Provider store={mockStore}>
    <ThemeProvider theme={mockTheme}>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="AccountSettings" component={() => children} />
        </Stack.Navigator>
      </NavigationContainer>
    </ThemeProvider>
  </Provider>
);

describe('AccountSettingsScreen', () => {
  const mockNavigation = mockNavigationProps();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders account settings screen with header and user profile', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      // Check header
      expect(screen.getByText('Account Settings')).toBeTruthy();
      expect(screen.getByTestId('settings-back-button')).toBeTruthy();

      // Check user profile section
      expect(screen.getByText('John Doe')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
      expect(screen.getByText('Customer')).toBeTruthy();
    });

    it('displays user avatar or initials', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      // Should display user initials when avatar is present
      expect(screen.getByText('JD')).toBeTruthy();
    });

    it('renders all settings sections', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      // Check section titles
      expect(screen.getByText('Profile Management')).toBeTruthy();
      expect(screen.getByText('Booking & Services')).toBeTruthy();
      expect(screen.getByText('Notifications')).toBeTruthy();
      expect(screen.getByText('App Preferences')).toBeTruthy();
      expect(screen.getByText('Privacy & Security')).toBeTruthy();
    });
  });

  describe('Profile Management Section', () => {
    it('navigates to edit profile screen', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const editProfileButton = screen.getByTestId('setting-edit-profile');
      fireEvent.press(editProfileButton);

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('EditProfile');
      });
    });

    it('navigates to change password screen', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const changePasswordButton = screen.getByTestId('setting-change-password');
      fireEvent.press(changePasswordButton);

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('ChangePassword');
      });
    });

    it('navigates to payment methods screen', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const paymentMethodsButton = screen.getByTestId('setting-payment-methods');
      fireEvent.press(paymentMethodsButton);

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('PaymentMethods');
      });
    });

    it('shows role switch confirmation dialog', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const roleSwitchButton = screen.getByTestId('setting-role-switch');
      fireEvent.press(roleSwitchButton);

      await waitFor(() => {
        expect(screen.getByText('Switch to Provider')).toBeTruthy();
        expect(screen.getByText('Would you like to switch to provider mode to offer services?')).toBeTruthy();
      });
    });
  });

  describe('Booking & Services Section', () => {
    it('navigates to booking history screen', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const bookingHistoryButton = screen.getByTestId('setting-booking-history');
      fireEvent.press(bookingHistoryButton);

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('Bookings');
      });
    });

    it('navigates to favorite providers screen', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const favoriteProvidersButton = screen.getByTestId('setting-favorite-providers');
      fireEvent.press(favoriteProvidersButton);

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('FavoriteProviders');
      });
    });

    it('toggles booking reminders setting', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const bookingRemindersToggle = screen.getByTestId('setting-auto-booking-reminders');
      const toggle = bookingRemindersToggle.findByType('Switch');
      
      expect(toggle.props.value).toBe(true); // Default value

      fireEvent(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });
  });

  describe('Notifications Section', () => {
    it('navigates to notification settings screen', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const notificationSettingsButton = screen.getByTestId('setting-notification-settings');
      fireEvent.press(notificationSettingsButton);

      await waitFor(() => {
        expect(mockNavigation.navigate).toHaveBeenCalledWith('NotificationSettings');
      });
    });

    it('toggles email notifications', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const emailNotificationsToggle = screen.getByTestId('setting-email-notifications');
      const toggle = emailNotificationsToggle.findByType('Switch');
      
      fireEvent(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });

    it('toggles push notifications', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const pushNotificationsToggle = screen.getByTestId('setting-push-notifications');
      const toggle = pushNotificationsToggle.findByType('Switch');
      
      fireEvent(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });
  });

  describe('App Preferences Section', () => {
    it('toggles dark mode', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const darkModeToggle = screen.getByTestId('setting-dark-mode');
      const toggle = darkModeToggle.findByType('Switch');
      
      fireEvent(toggle, 'onValueChange', true);
      // Theme change would be handled by the theme context
    });

    it('toggles location services', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const locationToggle = screen.getByTestId('setting-location');
      const toggle = locationToggle.findByType('Switch');
      
      fireEvent(toggle, 'onValueChange', false);
      expect(toggle.props.value).toBe(false);
    });

    it('toggles biometric authentication', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const biometricToggle = screen.getByTestId('setting-biometric');
      const toggle = biometricToggle.findByType('Switch');
      
      fireEvent(toggle, 'onValueChange', true);
      expect(toggle.props.value).toBe(true);
    });
  });

  describe('Privacy & Security Section', () => {
    it('shows privacy settings coming soon dialog', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const privacySettingsButton = screen.getByTestId('setting-privacy-settings');
      fireEvent.press(privacySettingsButton);

      await waitFor(() => {
        expect(screen.getByText('Privacy Settings')).toBeTruthy();
        expect(screen.getByText('Advanced privacy settings will be available soon.')).toBeTruthy();
      });
    });

    it('shows data export coming soon dialog', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const dataExportButton = screen.getByTestId('setting-data-export');
      fireEvent.press(dataExportButton);

      await waitFor(() => {
        expect(screen.getByText('Export Data')).toBeTruthy();
        expect(screen.getByText('Data export functionality will be available soon.')).toBeTruthy();
      });
    });
  });

  describe('Logout Functionality', () => {
    it('shows logout confirmation dialog', async () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      // Assuming there's a logout button in the settings
      const logoutButton = screen.getByTestId('setting-logout');
      fireEvent.press(logoutButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Out')).toBeTruthy();
        expect(screen.getByText('Are you sure you want to sign out?')).toBeTruthy();
      });
    });

    it('performs logout when confirmed', async () => {
      const mockLogout = jest.fn();
      const storeWithLogout = createMockStore({
        auth: {
          user: { id: 'user-1', firstName: 'John', lastName: 'Doe' },
          isAuthenticated: true,
          logout: mockLogout,
        },
      });

      render(
        <Provider store={storeWithLogout}>
          <ThemeProvider theme={mockTheme}>
            <AccountSettingsScreen navigation={mockNavigation} />
          </ThemeProvider>
        </Provider>
      );

      const logoutButton = screen.getByTestId('setting-logout');
      fireEvent.press(logoutButton);

      await waitFor(() => {
        const confirmButton = screen.getByText('Sign Out');
        fireEvent.press(confirmButton);
      });

      expect(mockLogout).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper accessibility labels for all interactive elements', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const editProfileButton = screen.getByTestId('setting-edit-profile');
      expect(editProfileButton.props.accessibilityLabel).toBe('Edit Profile');
      expect(editProfileButton.props.accessibilityHint).toBe('Update your personal information and preferences');
    });

    it('supports screen reader navigation', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const screenReader = screen.getByTestId('account-settings-main');
      expect(screenReader.props.announceOnMount).toBe('Account settings screen loaded. Manage your profile and preferences.');
    });

    it('has proper touch target sizes', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const settingItems = screen.getAllByTestId(/^setting-/);
      settingItems.forEach(item => {
        expect(item.props.minimumSize).toBe(64);
      });
    });
  });

  describe('Performance', () => {
    it('tracks user interactions for analytics', async () => {
      const mockTrackUserInteraction = jest.fn((name, fn) => fn());
      
      jest.doMock('../../hooks/usePerformance', () => ({
        usePerformance: () => ({
          trackUserInteraction: mockTrackUserInteraction,
        }),
      }));

      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const editProfileButton = screen.getByTestId('setting-edit-profile');
      fireEvent.press(editProfileButton);

      expect(mockTrackUserInteraction).toHaveBeenCalledWith('edit_profile', expect.any(Function));
    });

    it('implements proper error handling', async () => {
      const mockHandleError = jest.fn();
      
      jest.doMock('../../hooks/useErrorHandling', () => ({
        useErrorHandling: () => ({
          handleError: mockHandleError,
          clearError: jest.fn(),
        }),
      }));

      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      // Error handling would be tested when actual errors occur
      expect(mockHandleError).not.toHaveBeenCalled();
    });
  });

  describe('Navigation', () => {
    it('navigates back when back button is pressed', () => {
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      const backButton = screen.getByTestId('settings-back-button');
      fireEvent.press(backButton);

      expect(mockNavigation.goBack).toHaveBeenCalled();
    });

    it('handles deep linking to specific settings sections', () => {
      // This would test deep linking functionality if implemented
      render(
        <TestWrapper>
          <AccountSettingsScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      // Test would verify that the screen can handle navigation to specific sections
      expect(screen.getByText('Account Settings')).toBeTruthy();
    });
  });
});
