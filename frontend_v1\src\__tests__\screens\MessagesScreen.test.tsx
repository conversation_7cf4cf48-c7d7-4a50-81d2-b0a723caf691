/**
 * Enhanced Messages Screen Test Suite - Comprehensive Testing with Aura Design System
 *
 * Test Contract:
 * - Tests MessagesScreen component with backend integration and real-time features
 * - Validates conversation management, message handling, and WebSocket functionality
 * - Tests accessibility compliance, error handling, and user interactions
 * - Ensures proper state management and performance optimization
 * - Validates search functionality, typing indicators, and message status
 *
 * @version 3.0.0 - Enhanced with Comprehensive Testing for Aura Design System
 * <AUTHOR> Development Team
 */

import React from 'react';
import { render, fireEvent, waitFor, screen, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Component under test
import { MessagesScreen } from '../../screens/MessagesScreen';

// Test utilities and mocks
import { mockNavigationProps, createMockStore } from '../utils/testUtils';
import { mockConversations, mockMessages } from '../__mocks__/messagingData';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { mockTheme } from '../__mocks__/theme';

// Mock services
jest.mock('../../services/messagingService', () => ({
  getConversations: jest.fn(),
  getMessages: jest.fn(),
  sendMessage: jest.fn(),
  markAsRead: jest.fn(),
}));

jest.mock('../../services/websocketService', () => ({
  connect: jest.fn(),
  disconnect: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  emit: jest.fn(),
}));

jest.mock('../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    trackUserInteraction: jest.fn((name, fn) => fn()),
    measureRenderTime: jest.fn(),
    trackMemoryUsage: jest.fn(),
  }),
}));

jest.mock('../../hooks/useErrorHandling', () => ({
  useErrorHandling: () => ({
    handleError: jest.fn(),
    clearError: jest.fn(),
    isError: false,
    error: null,
  }),
}));

// Mock store
const mockStore = createMockStore({
  auth: {
    user: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    isAuthenticated: true,
  },
});

const Stack = createStackNavigator();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Provider store={mockStore}>
    <ThemeProvider theme={mockTheme}>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="Messages" component={() => children} />
        </Stack.Navigator>
      </NavigationContainer>
    </ThemeProvider>
  </Provider>
);

describe('MessagesScreen', () => {
  const mockMessagingService = require('../../services/messagingService');
  const mockWebSocketService = require('../../services/websocketService');

  beforeEach(() => {
    jest.clearAllMocks();
    mockMessagingService.getConversations.mockResolvedValue({
      conversations: mockConversations,
      total: mockConversations.length,
    });
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('Component Rendering', () => {
    it('renders messages screen with header and conversation list', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      // Check header elements
      expect(screen.getByText('Messages')).toBeTruthy();
      expect(screen.getByTestId('messages-search-input')).toBeTruthy();

      // Wait for conversations to load
      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });
    });

    it('displays loading state initially', () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      expect(screen.getByTestId('messages-loading-indicator')).toBeTruthy();
    });

    it('displays empty state when no conversations exist', async () => {
      mockMessagingService.getConversations.mockResolvedValue({
        conversations: [],
        total: 0,
      });

      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('No conversations yet')).toBeTruthy();
        expect(screen.getByText('Start a conversation with a service provider')).toBeTruthy();
      });
    });
  });

  describe('Conversation Management', () => {
    it('loads conversations on mount', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockMessagingService.getConversations).toHaveBeenCalledWith(1, 20);
      });
    });

    it('displays conversation list with proper information', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        // Check conversation items
        expect(screen.getByText('John Smith')).toBeTruthy();
        expect(screen.getByText('Hello, I need help with...')).toBeTruthy();
        expect(screen.getByText('2 min ago')).toBeTruthy();
      });
    });

    it('navigates to chat screen when conversation is pressed', async () => {
      const mockNavigation = mockNavigationProps();
      
      render(
        <TestWrapper>
          <MessagesScreen navigation={mockNavigation} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('conversation-item-1'));

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Chat', {
        providerId: 'provider-1',
        providerName: 'John Smith',
        conversationId: 'conv-1',
        providerAvatar: 'https://example.com/avatar1.jpg',
      });
    });
  });

  describe('Search Functionality', () => {
    it('filters conversations based on search query', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
        expect(screen.getByText('Jane Doe')).toBeTruthy();
      });

      const searchInput = screen.getByTestId('messages-search-input');
      fireEvent.changeText(searchInput, 'John');

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
        expect(screen.queryByText('Jane Doe')).toBeNull();
      });
    });

    it('shows no results message when search yields no matches', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });

      const searchInput = screen.getByTestId('messages-search-input');
      fireEvent.changeText(searchInput, 'NonExistentProvider');

      await waitFor(() => {
        expect(screen.getByText('No conversations found')).toBeTruthy();
      });
    });

    it('clears search when search input is cleared', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      const searchInput = screen.getByTestId('messages-search-input');
      
      // Search for something
      fireEvent.changeText(searchInput, 'John');
      await waitFor(() => {
        expect(screen.queryByText('Jane Doe')).toBeNull();
      });

      // Clear search
      fireEvent.changeText(searchInput, '');
      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
        expect(screen.getByText('Jane Doe')).toBeTruthy();
      });
    });
  });

  describe('Real-time Features', () => {
    it('sets up WebSocket connection on mount', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockWebSocketService.connect).toHaveBeenCalled();
        expect(mockWebSocketService.on).toHaveBeenCalledWith('chat_message', expect.any(Function));
        expect(mockWebSocketService.on).toHaveBeenCalledWith('conversation_updated', expect.any(Function));
        expect(mockWebSocketService.on).toHaveBeenCalledWith('typing_indicator', expect.any(Function));
      });
    });

    it('updates conversation list when new message is received', async () => {
      let messageHandler: Function;
      mockWebSocketService.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'chat_message') {
          messageHandler = handler;
        }
      });

      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Hello, I need help with...')).toBeTruthy();
      });

      // Simulate new message
      act(() => {
        messageHandler({
          conversation_id: 'conv-1',
          content: 'New message received',
          created_at: new Date().toISOString(),
        });
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeTruthy();
      });
    });

    it('displays typing indicators when provider is typing', async () => {
      let typingHandler: Function;
      mockWebSocketService.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'typing_indicator') {
          typingHandler = handler;
        }
      });

      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });

      // Simulate typing indicator
      act(() => {
        typingHandler({
          conversation_id: 'conv-1',
          user_id: 'provider-1',
          is_typing: true,
        });
      });

      await waitFor(() => {
        expect(screen.getByTestId('typing-indicator-conv-1')).toBeTruthy();
      });
    });
  });

  describe('Error Handling', () => {
    it('displays error message when conversation loading fails', async () => {
      mockMessagingService.getConversations.mockRejectedValue(new Error('Network error'));

      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Failed to load conversations')).toBeTruthy();
        expect(screen.getByText('Try Again')).toBeTruthy();
      });
    });

    it('allows retry when conversation loading fails', async () => {
      mockMessagingService.getConversations
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          conversations: mockConversations,
          total: mockConversations.length,
        });

      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Try Again')).toBeTruthy();
      });

      fireEvent.press(screen.getByText('Try Again'));

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper accessibility labels and hints', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const searchInput = screen.getByTestId('messages-search-input');
        expect(searchInput.props.accessibilityLabel).toBe('Search conversations');
        expect(searchInput.props.accessibilityHint).toBe('Type to search through your conversations');
      });
    });

    it('announces screen content to screen readers', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const screenReader = screen.getByTestId('messages-screen-reader');
        expect(screenReader.props.announceOnMount).toBe('Messages screen loaded. View and manage your conversations.');
      });
    });

    it('supports keyboard navigation for conversation items', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        const conversationItem = screen.getByTestId('conversation-item-1');
        expect(conversationItem.props.accessible).toBe(true);
        expect(conversationItem.props.accessibilityRole).toBe('button');
      });
    });
  });

  describe('Performance', () => {
    it('tracks user interactions for analytics', async () => {
      const mockTrackUserInteraction = jest.fn((name, fn) => fn());
      
      jest.doMock('../../hooks/usePerformance', () => ({
        usePerformance: () => ({
          trackUserInteraction: mockTrackUserInteraction,
        }),
      }));

      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('John Smith')).toBeTruthy();
      });

      fireEvent.press(screen.getByTestId('conversation-item-1'));

      expect(mockTrackUserInteraction).toHaveBeenCalledWith('open_conversation', expect.any(Function));
    });

    it('implements pull-to-refresh functionality', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      const scrollView = screen.getByTestId('messages-scroll-view');
      
      // Simulate pull to refresh
      fireEvent(scrollView, 'refresh');

      await waitFor(() => {
        expect(mockMessagingService.getConversations).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('State Management', () => {
    it('manages unread counts correctly', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('2')).toBeTruthy(); // Unread count badge
      });
    });

    it('updates last seen timestamps', async () => {
      render(
        <TestWrapper>
          <MessagesScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('2 min ago')).toBeTruthy();
      });
    });
  });
});
