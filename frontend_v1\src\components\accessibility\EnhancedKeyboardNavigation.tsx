/**
 * Enhanced Keyboard Navigation Component
 * Provides comprehensive keyboard navigation with no keyboard traps
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, Platform, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { FocusUtils } from '../../utils/globalFocusManager';
import { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';

export interface KeyboardNavigationConfig {
  enableArrowKeys: boolean;
  enableTabNavigation: boolean;
  enableEscapeKey: boolean;
  enableHomeEndKeys: boolean;
  enableSkipLinks: boolean;
  preventTraps: boolean;
  announceChanges: boolean;
  customShortcuts: KeyboardShortcut[];
}

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  global?: boolean;
}

export interface FocusableElement {
  id: string;
  ref: React.RefObject<any>;
  type: 'button' | 'input' | 'link' | 'custom';
  disabled?: boolean;
  skipable?: boolean;
  priority?: number;
}

export interface EnhancedKeyboardNavigationProps {
  children: React.ReactNode;
  config?: Partial<KeyboardNavigationConfig>;
  focusableElements?: FocusableElement[];
  onFocusChange?: (elementId: string | null) => void;
  onKeyboardTrap?: (trapInfo: any) => void;
  enableFocusTrapping?: boolean;
  trapBoundary?: 'container' | 'screen' | 'modal';
  autoFocus?: string; // Element ID to auto-focus
  restoreFocusOnUnmount?: boolean;
}

const defaultConfig: KeyboardNavigationConfig = {
  enableArrowKeys: true,
  enableTabNavigation: true,
  enableEscapeKey: true,
  enableHomeEndKeys: true,
  enableSkipLinks: true,
  preventTraps: true,
  announceChanges: true,
  customShortcuts: [],
};

export const EnhancedKeyboardNavigation: React.FC<EnhancedKeyboardNavigationProps> = ({
  children,
  config = {},
  focusableElements = [],
  onFocusChange,
  onKeyboardTrap,
  enableFocusTrapping = false,
  trapBoundary = 'container',
  autoFocus,
  restoreFocusOnUnmount = true,
}) => {
  const { colors } = useTheme();
  const containerRef = useRef<View>(null);
  const [currentFocusIndex, setCurrentFocusIndex] = useState(-1);
  const [focusHistory, setFocusHistory] = useState<string[]>([]);
  const [isKeyboardUser, setIsKeyboardUser] = useState(false);
  const previousFocusRef = useRef<string | null>(null);

  const finalConfig = { ...defaultConfig, ...config };

  // Get focusable elements in order
  const getFocusableElements = useCallback(() => {
    return focusableElements
      .filter(el => !el.disabled && !el.skipable)
      .sort((a, b) => (a.priority || 0) - (b.priority || 0));
  }, [focusableElements]);

  // Focus management functions
  const focusElement = useCallback((elementId: string) => {
    const element = focusableElements.find(el => el.id === elementId);
    if (element && element.ref.current) {
      FocusUtils.setGlobalFocus(elementId);
      
      if (Platform.OS === 'web' && element.ref.current.focus) {
        element.ref.current.focus();
      }

      setFocusHistory(prev => [...prev.slice(-9), elementId]); // Keep last 10
      onFocusChange?.(elementId);

      if (finalConfig.announceChanges) {
        // Announce focus change to screen readers
        const announcement = `Focused ${element.type} ${elementId}`;
        // Would use screen reader announcement here
      }
    }
  }, [focusableElements, onFocusChange, finalConfig.announceChanges]);

  const moveFocus = useCallback((direction: 'next' | 'previous' | 'first' | 'last') => {
    const focusable = getFocusableElements();
    if (focusable.length === 0) return false;

    let newIndex = currentFocusIndex;

    switch (direction) {
      case 'next':
        newIndex = currentFocusIndex < focusable.length - 1 ? currentFocusIndex + 1 : 0;
        break;
      case 'previous':
        newIndex = currentFocusIndex > 0 ? currentFocusIndex - 1 : focusable.length - 1;
        break;
      case 'first':
        newIndex = 0;
        break;
      case 'last':
        newIndex = focusable.length - 1;
        break;
    }

    if (newIndex !== currentFocusIndex) {
      setCurrentFocusIndex(newIndex);
      focusElement(focusable[newIndex].id);
      return true;
    }

    return false;
  }, [currentFocusIndex, getFocusableElements, focusElement]);

  // Keyboard trap detection and prevention
  const detectKeyboardTrap = useCallback(() => {
    if (!finalConfig.preventTraps) return false;

    const focusable = getFocusableElements();
    if (focusable.length === 0) return false;

    // Check if focus is trapped (simplified detection)
    const currentElement = focusable[currentFocusIndex];
    if (!currentElement) return false;

    // In a real implementation, this would check if the user can navigate
    // out of the current focus area using standard keyboard navigation
    const isTrapped = false; // Simplified

    if (isTrapped && onKeyboardTrap) {
      onKeyboardTrap({
        elementId: currentElement.id,
        trapType: 'focus_trap',
        boundary: trapBoundary,
      });
    }

    return isTrapped;
  }, [finalConfig.preventTraps, getFocusableElements, currentFocusIndex, onKeyboardTrap, trapBoundary]);

  // Keyboard event handlers
  const handleKeyDown = useCallback((event: any) => {
    if (!isKeyboardUser) {
      setIsKeyboardUser(true);
    }

    // Handle custom shortcuts first
    for (const shortcut of finalConfig.customShortcuts) {
      if (
        event.key === shortcut.key &&
        !!event.ctrlKey === !!shortcut.ctrlKey &&
        !!event.shiftKey === !!shortcut.shiftKey &&
        !!event.altKey === !!shortcut.altKey &&
        !!event.metaKey === !!shortcut.metaKey
      ) {
        event.preventDefault();
        shortcut.action();
        return;
      }
    }

    // Handle standard navigation keys
    switch (event.key) {
      case 'Tab':
        if (finalConfig.enableTabNavigation) {
          event.preventDefault();
          const direction = event.shiftKey ? 'previous' : 'next';
          moveFocus(direction);
        }
        break;

      case 'ArrowUp':
      case 'ArrowDown':
        if (finalConfig.enableArrowKeys) {
          event.preventDefault();
          const direction = event.key === 'ArrowUp' ? 'previous' : 'next';
          moveFocus(direction);
        }
        break;

      case 'ArrowLeft':
      case 'ArrowRight':
        if (finalConfig.enableArrowKeys) {
          event.preventDefault();
          const direction = event.key === 'ArrowLeft' ? 'previous' : 'next';
          moveFocus(direction);
        }
        break;

      case 'Home':
        if (finalConfig.enableHomeEndKeys) {
          event.preventDefault();
          moveFocus('first');
        }
        break;

      case 'End':
        if (finalConfig.enableHomeEndKeys) {
          event.preventDefault();
          moveFocus('last');
        }
        break;

      case 'Escape':
        if (finalConfig.enableEscapeKey) {
          event.preventDefault();
          // Restore previous focus or clear focus
          if (focusHistory.length > 1) {
            const previousFocus = focusHistory[focusHistory.length - 2];
            focusElement(previousFocus);
          } else {
            FocusUtils.clearGlobalFocus();
            setCurrentFocusIndex(-1);
            onFocusChange?.(null);
          }
        }
        break;

      case 'Enter':
      case ' ':
        // Let the focused element handle activation
        break;
    }

    // Check for keyboard traps after navigation
    setTimeout(detectKeyboardTrap, 0);
  }, [
    isKeyboardUser,
    finalConfig,
    moveFocus,
    focusHistory,
    focusElement,
    onFocusChange,
    detectKeyboardTrap,
  ]);

  // Auto-focus on mount
  useEffect(() => {
    if (autoFocus) {
      const timer = setTimeout(() => {
        focusElement(autoFocus);
      }, 100); // Small delay to ensure elements are rendered

      return () => clearTimeout(timer);
    }
  }, [autoFocus, focusElement]);

  // Store previous focus for restoration
  useEffect(() => {
    if (restoreFocusOnUnmount) {
      previousFocusRef.current = FocusUtils.isGloballyFocused('') ? '' : null;
    }

    return () => {
      if (restoreFocusOnUnmount && previousFocusRef.current) {
        FocusUtils.setGlobalFocus(previousFocusRef.current);
      }
    };
  }, [restoreFocusOnUnmount]);

  // Web-specific keyboard event handling
  useEffect(() => {
    if (Platform.OS === 'web') {
      const handleKeyDownWeb = (event: KeyboardEvent) => {
        handleKeyDown(event);
      };

      document.addEventListener('keydown', handleKeyDownWeb);
      return () => document.removeEventListener('keydown', handleKeyDownWeb);
    }
  }, [handleKeyDown]);

  // Focus trap implementation
  useEffect(() => {
    if (enableFocusTrapping && Platform.OS === 'web') {
      const handleFocusIn = (event: FocusEvent) => {
        const target = event.target as HTMLElement;
        const container = containerRef.current as any;

        if (container && !container.contains(target)) {
          // Focus escaped the trap, bring it back
          event.preventDefault();
          const focusable = getFocusableElements();
          if (focusable.length > 0) {
            focusElement(focusable[0].id);
          }
        }
      };

      document.addEventListener('focusin', handleFocusIn);
      return () => document.removeEventListener('focusin', handleFocusIn);
    }
  }, [enableFocusTrapping, getFocusableElements, focusElement]);

  // Skip links implementation
  const renderSkipLinks = () => {
    if (!finalConfig.enableSkipLinks || Platform.OS !== 'web') return null;

    return (
      <View
        style={{
          position: 'absolute',
          top: -1000,
          left: 0,
          width: 1,
          height: 1,
          overflow: 'hidden',
        }}
      >
        {/* Skip links would be rendered here for web */}
      </View>
    );
  };

  return (
    <View
      ref={containerRef}
      style={{ flex: 1 }}
      accessible={false} // Let children handle their own accessibility
    >
      {renderSkipLinks()}
      {children}
      
      {/* Development indicator */}
      {__DEV__ && isKeyboardUser && (
        <View
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            backgroundColor: '#2196F3',
            padding: 4,
            borderRadius: 4,
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: '#FFFFFF',
                marginRight: 4,
              }}
            />
          </View>
        </View>
      )}
    </View>
  );
};

// Hook for registering focusable elements
export const useFocusableElement = (
  id: string,
  type: FocusableElement['type'],
  options: Partial<Pick<FocusableElement, 'disabled' | 'skipable' | 'priority'>> = {}
) => {
  const ref = useRef<any>(null);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    const checkFocus = () => {
      const focused = FocusUtils.isGloballyFocused(id);
      setIsFocused(focused);
    };

    // Check focus periodically (in a real implementation, this would be event-driven)
    const interval = setInterval(checkFocus, 100);
    return () => clearInterval(interval);
  }, [id]);

  const focusProps = {
    ref,
    onFocus: () => {
      FocusUtils.setGlobalFocus(id);
      setIsFocused(true);
    },
    onBlur: () => {
      setIsFocused(false);
    },
    style: isFocused ? FocusUtils.generateFocusStyles(id) : {},
  };

  return {
    ref,
    isFocused,
    focusProps,
    element: {
      id,
      ref,
      type,
      ...options,
    },
  };
};
