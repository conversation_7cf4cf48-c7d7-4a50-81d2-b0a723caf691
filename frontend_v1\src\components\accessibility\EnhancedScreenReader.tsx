/**
 * Enhanced Screen Reader Support Component
 * Provides comprehensive screen reader support with semantic markup and announcements
 */

import React, { useEffect, useRef, useState } from 'react';
import { View, Text, AccessibilityInfo, Platform } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { ScreenReaderUtils } from '../../utils/accessibility';

export interface ScreenReaderRegion {
  id: string;
  label: string;
  role: 'main' | 'navigation' | 'banner' | 'contentinfo' | 'complementary' | 'region';
  description?: string;
  live?: 'off' | 'polite' | 'assertive';
}

export interface EnhancedScreenReaderProps {
  children: React.ReactNode;
  region?: ScreenReaderRegion;
  announceOnMount?: string;
  announceOnUnmount?: string;
  skipToContent?: boolean;
  landmarkNavigation?: boolean;
  autoDescribe?: boolean;
  verbosityLevel?: 'minimal' | 'standard' | 'verbose';
}

export const EnhancedScreenReader: React.FC<EnhancedScreenReaderProps> = ({
  children,
  region,
  announceOnMount,
  announceOnUnmount,
  skipToContent = false,
  landmarkNavigation = true,
  autoDescribe = true,
  verbosityLevel = 'standard',
}) => {
  const { colors } = useTheme();
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);
  const [currentFocus, setCurrentFocus] = useState<string | null>(null);
  const regionRef = useRef<View>(null);
  const skipLinkRef = useRef<View>(null);

  useEffect(() => {
    // Check screen reader status
    const checkScreenReader = async () => {
      try {
        const enabled = await AccessibilityInfo.isScreenReaderEnabled();
        setIsScreenReaderEnabled(enabled);
      } catch (error) {
        console.warn('Failed to check screen reader status:', error);
      }
    };

    checkScreenReader();

    // Listen for screen reader changes
    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setIsScreenReaderEnabled
    );

    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    if (isScreenReaderEnabled && announceOnMount) {
      const message = getAnnouncementMessage(announceOnMount);
      ScreenReaderUtils.announceForAccessibility(message);
    }

    return () => {
      if (isScreenReaderEnabled && announceOnUnmount) {
        const message = getAnnouncementMessage(announceOnUnmount);
        ScreenReaderUtils.announceForAccessibility(message);
      }
    };
  }, [isScreenReaderEnabled, announceOnMount, announceOnUnmount]);

  const getAnnouncementMessage = (message: string): string => {
    switch (verbosityLevel) {
      case 'minimal':
        return message;
      case 'verbose':
        return `${region?.label ? `${region.label}: ` : ''}${message}${
          region?.description ? `. ${region.description}` : ''
        }`;
      case 'standard':
      default:
        return `${region?.label ? `${region.label}: ` : ''}${message}`;
    }
  };

  const getRegionAccessibilityProps = () => {
    if (!region) return {};

    const props: any = {
      accessibilityRole: region.role,
      accessibilityLabel: region.label,
    };

    if (region.description) {
      props.accessibilityHint = region.description;
    }

    if (region.live) {
      props.accessibilityLiveRegion = region.live;
    }

    return props;
  };

  const renderSkipToContent = () => {
    if (!skipToContent || !isScreenReaderEnabled) return null;

    return (
      <View
        ref={skipLinkRef}
        style={{
          position: 'absolute',
          top: -1000,
          left: 0,
          width: 1,
          height: 1,
          overflow: 'hidden',
        }}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel="Skip to main content"
        accessibilityHint="Activate to jump to the main content area"
        onAccessibilityTap={() => {
          if (regionRef.current) {
            // Focus the main content region
            AccessibilityInfo.setAccessibilityFocus(regionRef.current as any);
            ScreenReaderUtils.announceForAccessibility('Skipped to main content');
          }
        }}
      >
        <Text style={{ fontSize: 1, color: 'transparent' }}>Skip to content</Text>
      </View>
    );
  };

  const renderLandmarkNavigation = () => {
    if (!landmarkNavigation || !isScreenReaderEnabled) return null;

    return (
      <View
        style={{
          position: 'absolute',
          top: -1000,
          left: 0,
          width: 1,
          height: 1,
          overflow: 'hidden',
        }}
        accessible={true}
        accessibilityRole="navigation"
        accessibilityLabel="Landmark navigation"
        accessibilityHint="Navigate between page landmarks"
      >
        <Text style={{ fontSize: 1, color: 'transparent' }}>Landmarks</Text>
      </View>
    );
  };

  const renderAutoDescription = () => {
    if (!autoDescribe || !isScreenReaderEnabled || !region) return null;

    const description = generateAutoDescription();
    if (!description) return null;

    return (
      <View
        style={{
          position: 'absolute',
          top: -1000,
          left: 0,
          width: 1,
          height: 1,
          overflow: 'hidden',
        }}
        accessible={true}
        accessibilityLabel={description}
        accessibilityRole="text"
      >
        <Text style={{ fontSize: 1, color: 'transparent' }}>{description}</Text>
      </View>
    );
  };

  const generateAutoDescription = (): string | null => {
    if (!region) return null;

    const descriptions: string[] = [];

    // Add region type description
    switch (region.role) {
      case 'main':
        descriptions.push('Main content area');
        break;
      case 'navigation':
        descriptions.push('Navigation menu');
        break;
      case 'banner':
        descriptions.push('Page header');
        break;
      case 'contentinfo':
        descriptions.push('Page footer');
        break;
      case 'complementary':
        descriptions.push('Sidebar content');
        break;
      case 'region':
        descriptions.push('Content region');
        break;
    }

    // Add custom description if provided
    if (region.description) {
      descriptions.push(region.description);
    }

    return descriptions.length > 0 ? descriptions.join('. ') : null;
  };

  // Enhanced accessibility props for the container
  const containerAccessibilityProps = {
    ...getRegionAccessibilityProps(),
    accessible: true,
    accessibilityElementsHidden: false,
    importantForAccessibility: 'yes' as const,
  };

  return (
    <View
      ref={regionRef}
      {...containerAccessibilityProps}
      style={{ flex: 1 }}
    >
      {renderSkipToContent()}
      {renderLandmarkNavigation()}
      {renderAutoDescription()}
      
      {/* Screen reader status indicator for development */}
      {__DEV__ && (
        <View
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            backgroundColor: isScreenReaderEnabled ? '#4CAF50' : '#FF6B6B',
            padding: 4,
            borderRadius: 4,
            zIndex: 1000,
          }}
        >
          <Text style={{ color: '#FFFFFF', fontSize: 10 }}>
            SR: {isScreenReaderEnabled ? 'ON' : 'OFF'}
          </Text>
        </View>
      )}

      {children}
    </View>
  );
};

// Screen Reader Announcement Hook
export const useScreenReaderAnnouncement = () => {
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);

  useEffect(() => {
    const checkScreenReader = async () => {
      try {
        const enabled = await AccessibilityInfo.isScreenReaderEnabled();
        setIsScreenReaderEnabled(enabled);
      } catch (error) {
        console.warn('Failed to check screen reader status:', error);
      }
    };

    checkScreenReader();

    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setIsScreenReaderEnabled
    );

    return () => subscription?.remove();
  }, []);

  const announce = (
    message: string,
    priority: 'polite' | 'assertive' = 'polite',
    delay: number = 0
  ) => {
    if (!isScreenReaderEnabled) return;

    const announceMessage = () => {
      ScreenReaderUtils.announceForAccessibility(message);
    };

    if (delay > 0) {
      setTimeout(announceMessage, delay);
    } else {
      announceMessage();
    }
  };

  const announcePageChange = (pageName: string, description?: string) => {
    if (!isScreenReaderEnabled) return;

    const message = description 
      ? `${pageName}. ${description}` 
      : `Navigated to ${pageName}`;
    
    announce(message, 'assertive', 500); // Small delay for page transitions
  };

  const announceFormError = (fieldName: string, error: string) => {
    if (!isScreenReaderEnabled) return;

    announce(`Error in ${fieldName}: ${error}`, 'assertive');
  };

  const announceLoadingState = (isLoading: boolean, context?: string) => {
    if (!isScreenReaderEnabled) return;

    const message = isLoading 
      ? `Loading${context ? ` ${context}` : ''}...` 
      : `Loading complete${context ? ` for ${context}` : ''}`;
    
    announce(message, 'polite');
  };

  const announceSuccess = (action: string) => {
    if (!isScreenReaderEnabled) return;

    announce(`${action} successful`, 'assertive');
  };

  return {
    isScreenReaderEnabled,
    announce,
    announcePageChange,
    announceFormError,
    announceLoadingState,
    announceSuccess,
  };
};

// Live Region Component for dynamic content
export interface LiveRegionProps {
  children: React.ReactNode;
  politeness?: 'off' | 'polite' | 'assertive';
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
}

export const LiveRegion: React.FC<LiveRegionProps> = ({
  children,
  politeness = 'polite',
  atomic = false,
  relevant = 'all',
}) => {
  return (
    <View
      accessible={true}
      accessibilityLiveRegion={politeness}
      accessibilityRole="text"
      // Note: React Native doesn't support aria-atomic and aria-relevant directly
      // These would be handled in web implementation
    >
      {children}
    </View>
  );
};

// Semantic Heading Component
export interface SemanticHeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
  style?: any;
  accessibilityLabel?: string;
}

export const SemanticHeading: React.FC<SemanticHeadingProps> = ({
  level,
  children,
  style,
  accessibilityLabel,
}) => {
  const { colors } = useTheme();

  const getHeadingStyle = () => {
    const baseStyle = {
      color: colors.text.primary,
      fontWeight: 'bold' as const,
    };

    switch (level) {
      case 1:
        return { ...baseStyle, fontSize: 24 };
      case 2:
        return { ...baseStyle, fontSize: 20 };
      case 3:
        return { ...baseStyle, fontSize: 18 };
      case 4:
        return { ...baseStyle, fontSize: 16 };
      case 5:
        return { ...baseStyle, fontSize: 14 };
      case 6:
        return { ...baseStyle, fontSize: 12 };
      default:
        return { ...baseStyle, fontSize: 16 };
    }
  };

  return (
    <Text
      style={[getHeadingStyle(), style]}
      accessible={true}
      accessibilityRole="header"
      accessibilityLabel={accessibilityLabel}
      accessibilityLevel={level}
    >
      {children}
    </Text>
  );
};
