/**
 * Enhanced Touch Target Component
 * Ensures all interactive elements meet WCAG 2.1 AA touch target requirements (44x44px minimum)
 */

import React, { useState, useRef, useEffect } from 'react';
import { 
  TouchableOpacity, 
  View, 
  Text, 
  StyleSheet, 
  Dimensions,
  Platform,
  ViewStyle,
  TextStyle,
  GestureResponderEvent,
  PanResponder,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { AccessibilityUtils } from '../../utils/accessibilityUtils';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface EnhancedTouchTargetProps {
  children: React.ReactNode;
  onPress?: (event: GestureResponderEvent) => void;
  onLongPress?: (event: GestureResponderEvent) => void;
  onPressIn?: (event: GestureResponderEvent) => void;
  onPressOut?: (event: GestureResponderEvent) => void;
  disabled?: boolean;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: string;
  testID?: string;
  
  // WCAG 2.1 AA specific props
  minimumSize?: number; // Default 44px
  enforceMinimumSize?: boolean;
  showTouchFeedback?: boolean;
  touchFeedbackColor?: string;
  touchFeedbackOpacity?: number;
  
  // Enhanced accessibility props
  enableHapticFeedback?: boolean;
  enableSoundFeedback?: boolean;
  enableVisualFeedback?: boolean;
  feedbackDuration?: number;
  
  // Gesture support
  enableSwipeGestures?: boolean;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  swipeThreshold?: number;
  
  // Focus management
  autoFocus?: boolean;
  focusable?: boolean;
  tabIndex?: number;
}

export const EnhancedTouchTarget: React.FC<EnhancedTouchTargetProps> = ({
  children,
  onPress,
  onLongPress,
  onPressIn,
  onPressOut,
  disabled = false,
  style,
  contentStyle,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  testID,
  
  // WCAG props
  minimumSize = AccessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE,
  enforceMinimumSize = true,
  showTouchFeedback = true,
  touchFeedbackColor,
  touchFeedbackOpacity = 0.1,
  
  // Enhanced accessibility
  enableHapticFeedback = true,
  enableSoundFeedback = false,
  enableVisualFeedback = true,
  feedbackDuration = 150,
  
  // Gesture support
  enableSwipeGestures = false,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  swipeThreshold = 50,
  
  // Focus management
  autoFocus = false,
  focusable = true,
  tabIndex,
}) => {
  const { colors } = useTheme();
  const [isPressed, setIsPressed] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  const touchRef = useRef<TouchableOpacity>(null);
  const feedbackAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Calculate effective touch target size
  const getEffectiveTouchTargetStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {};
    
    if (enforceMinimumSize) {
      const minWidth = Math.max(dimensions.width, minimumSize);
      const minHeight = Math.max(dimensions.height, minimumSize);
      
      baseStyle.minWidth = minWidth;
      baseStyle.minHeight = minHeight;
      
      // Add padding if content is smaller than minimum size
      if (dimensions.width < minimumSize) {
        const horizontalPadding = (minimumSize - dimensions.width) / 2;
        baseStyle.paddingHorizontal = horizontalPadding;
      }
      
      if (dimensions.height < minimumSize) {
        const verticalPadding = (minimumSize - dimensions.height) / 2;
        baseStyle.paddingVertical = verticalPadding;
      }
    }
    
    return baseStyle;
  };

  // Get focus styles
  const getFocusStyle = (): ViewStyle => {
    if (!isFocused) return {};
    
    return {
      borderWidth: 2,
      borderColor: colors.primary.default,
      borderRadius: 4,
      shadowColor: colors.primary.default,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 4,
    };
  };

  // Handle press feedback
  const handlePressIn = (event: GestureResponderEvent) => {
    setIsPressed(true);
    
    if (enableVisualFeedback) {
      Animated.parallel([
        Animated.timing(feedbackAnimation, {
          toValue: 1,
          duration: feedbackDuration,
          useNativeDriver: false,
        }),
        Animated.timing(scaleAnimation, {
          toValue: 0.95,
          duration: feedbackDuration,
          useNativeDriver: true,
        }),
      ]).start();
    }
    
    if (enableHapticFeedback && Platform.OS === 'ios') {
      // Add haptic feedback for iOS
      // HapticFeedback.impact(HapticFeedback.ImpactFeedbackStyle.Light);
    }
    
    onPressIn?.(event);
  };

  const handlePressOut = (event: GestureResponderEvent) => {
    setIsPressed(false);
    
    if (enableVisualFeedback) {
      Animated.parallel([
        Animated.timing(feedbackAnimation, {
          toValue: 0,
          duration: feedbackDuration,
          useNativeDriver: false,
        }),
        Animated.timing(scaleAnimation, {
          toValue: 1,
          duration: feedbackDuration,
          useNativeDriver: true,
        }),
      ]).start();
    }
    
    onPressOut?.(event);
  };

  const handlePress = (event: GestureResponderEvent) => {
    if (enableHapticFeedback && Platform.OS === 'ios') {
      // Add haptic feedback for iOS
      // HapticFeedback.impact(HapticFeedback.ImpactFeedbackStyle.Medium);
    }
    
    onPress?.(event);
  };

  // Swipe gesture handler
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => enableSwipeGestures,
    onMoveShouldSetPanResponder: () => enableSwipeGestures,
    onPanResponderMove: () => {},
    onPanResponderRelease: (evt, gestureState) => {
      if (!enableSwipeGestures) return;
      
      const { dx, dy } = gestureState;
      const absDx = Math.abs(dx);
      const absDy = Math.abs(dy);
      
      if (absDx > swipeThreshold || absDy > swipeThreshold) {
        if (absDx > absDy) {
          // Horizontal swipe
          if (dx > 0) {
            onSwipeRight?.();
          } else {
            onSwipeLeft?.();
          }
        } else {
          // Vertical swipe
          if (dy > 0) {
            onSwipeDown?.();
          } else {
            onSwipeUp?.();
          }
        }
      }
    },
  });

  // Auto-focus effect
  useEffect(() => {
    if (autoFocus && touchRef.current) {
      setTimeout(() => {
        touchRef.current?.focus?.();
        setIsFocused(true);
      }, 100);
    }
  }, [autoFocus]);

  // Measure content dimensions
  const handleLayout = (event: any) => {
    const { width, height } = event.nativeEvent.layout;
    setDimensions({ width, height });
  };

  // Get accessibility props
  const getAccessibilityProps = () => {
    const props: any = {
      accessible: true,
      accessibilityRole,
      accessibilityLabel,
      accessibilityHint,
      accessibilityState: {
        disabled,
        selected: isPressed,
      },
    };

    if (Platform.OS === 'web' && tabIndex !== undefined) {
      props.tabIndex = disabled ? -1 : tabIndex;
    }

    return props;
  };

  // Render touch feedback overlay
  const renderTouchFeedback = () => {
    if (!showTouchFeedback || !enableVisualFeedback) return null;

    const feedbackColor = touchFeedbackColor || colors.primary.default;

    return (
      <Animated.View
        style={[
          StyleSheet.absoluteFillObject,
          {
            backgroundColor: feedbackColor,
            opacity: feedbackAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [0, touchFeedbackOpacity],
            }),
            borderRadius: 4,
          },
        ]}
        pointerEvents="none"
      />
    );
  };

  // Render size indicator for development
  const renderSizeIndicator = () => {
    if (!__DEV__ || !enforceMinimumSize) return null;

    const meetsMinimumSize = dimensions.width >= minimumSize && dimensions.height >= minimumSize;

    return (
      <View
        style={[
          styles.sizeIndicator,
          {
            backgroundColor: meetsMinimumSize ? '#4CAF50' : '#FF6B6B',
          },
        ]}
      >
        <Text style={styles.sizeIndicatorText}>
          {Math.round(dimensions.width)}x{Math.round(dimensions.height)}
        </Text>
      </View>
    );
  };

  const combinedStyle = [
    getEffectiveTouchTargetStyle(),
    getFocusStyle(),
    style,
    {
      opacity: disabled ? 0.6 : 1,
    },
  ];

  const touchableProps = {
    ref: touchRef,
    onPress: handlePress,
    onLongPress,
    onPressIn: handlePressIn,
    onPressOut: handlePressOut,
    disabled,
    style: combinedStyle,
    testID,
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
    ...getAccessibilityProps(),
    ...(enableSwipeGestures ? panResponder.panHandlers : {}),
  };

  return (
    <TouchableOpacity {...touchableProps}>
      <Animated.View
        style={[
          contentStyle,
          {
            transform: [{ scale: scaleAnimation }],
          },
        ]}
        onLayout={handleLayout}
      >
        {children}
        {renderTouchFeedback()}
        {renderSizeIndicator()}
      </Animated.View>
    </TouchableOpacity>
  );
};

// Convenience component for icon buttons
export interface AccessibleIconButtonProps extends Omit<EnhancedTouchTargetProps, 'children'> {
  iconName: string;
  iconSize?: number;
  iconColor?: string;
  label: string;
  showLabel?: boolean;
  labelPosition?: 'bottom' | 'right' | 'left' | 'top';
}

export const AccessibleIconButton: React.FC<AccessibleIconButtonProps> = ({
  iconName,
  iconSize = 24,
  iconColor,
  label,
  showLabel = false,
  labelPosition = 'bottom',
  ...touchTargetProps
}) => {
  const { colors } = useTheme();
  const finalIconColor = iconColor || colors.text.primary;

  const renderIcon = () => (
    <Ionicons
      name={iconName as any}
      size={iconSize}
      color={finalIconColor}
    />
  );

  const renderLabel = () => {
    if (!showLabel) return null;

    return (
      <Text
        style={[
          styles.iconButtonLabel,
          {
            color: colors.text.secondary,
            marginTop: labelPosition === 'bottom' ? 4 : 0,
            marginBottom: labelPosition === 'top' ? 4 : 0,
            marginLeft: labelPosition === 'right' ? 4 : 0,
            marginRight: labelPosition === 'left' ? 4 : 0,
          },
        ]}
      >
        {label}
      </Text>
    );
  };

  const getContentStyle = (): ViewStyle => {
    const isHorizontal = labelPosition === 'left' || labelPosition === 'right';
    
    return {
      flexDirection: isHorizontal ? 'row' : 'column',
      alignItems: 'center',
      justifyContent: 'center',
    };
  };

  return (
    <EnhancedTouchTarget
      {...touchTargetProps}
      accessibilityLabel={touchTargetProps.accessibilityLabel || label}
      contentStyle={getContentStyle()}
    >
      {labelPosition === 'top' && renderLabel()}
      {labelPosition === 'left' && renderLabel()}
      {renderIcon()}
      {labelPosition === 'right' && renderLabel()}
      {labelPosition === 'bottom' && renderLabel()}
    </EnhancedTouchTarget>
  );
};

const styles = StyleSheet.create({
  sizeIndicator: {
    position: 'absolute',
    top: -15,
    right: -5,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    zIndex: 1000,
  },
  sizeIndicatorText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: 'bold',
  },
  iconButtonLabel: {
    fontSize: getResponsiveFontSize(12),
    textAlign: 'center',
  },
});
