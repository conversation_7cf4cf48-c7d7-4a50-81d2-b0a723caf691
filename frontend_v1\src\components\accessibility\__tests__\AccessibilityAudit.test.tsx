/**
 * Accessibility Audit Test Suite
 * Comprehensive tests for the AccessibilityAudit component
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { AccessibilityAudit } from '../AccessibilityAudit';
import { AccessibilityUtils } from '../../../utils/accessibilityUtils';
import { ThemeProvider } from '../../../contexts/ThemeContext';

// Mock dependencies
jest.mock('../../../utils/accessibilityUtils');
jest.mock('../../../contexts/ThemeContext');

const mockAccessibilityUtils = AccessibilityUtils as jest.Mocked<typeof AccessibilityUtils>;

// Test wrapper
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    {children}
  </ThemeProvider>
);

describe('AccessibilityAudit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock AccessibilityUtils methods
    mockAccessibilityUtils.getContrastRatio.mockReturnValue(4.5);
    mockAccessibilityUtils.meetsWCAGAA.mockReturnValue(true);
    mockAccessibilityUtils.WCAG_STANDARDS = {
      TOUCH_TARGETS: {
        MINIMUM_SIZE: 44,
      },
    };
  });

  describe('Rendering', () => {
    it('should render audit component correctly', () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      expect(getByText('Accessibility Audit (WCAG AA)')).toBeTruthy();
      expect(getByText('Run Audit')).toBeTruthy();
    });

    it('should not render when showDetailedReport is false', () => {
      const { queryByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={false} />
        </TestWrapper>
      );

      expect(queryByText('Accessibility Audit (WCAG AA)')).toBeFalsy();
    });

    it('should render with different compliance levels', () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit complianceLevel="AAA" showDetailedReport={true} />
        </TestWrapper>
      );

      expect(getByText('Accessibility Audit (WCAG AAA)')).toBeTruthy();
    });
  });

  describe('Audit Execution', () => {
    it('should run audit when button is pressed', async () => {
      const mockOnAuditComplete = jest.fn();

      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit 
            showDetailedReport={true}
            onAuditComplete={mockOnAuditComplete}
          />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      expect(getByText('Auditing...')).toBeTruthy();

      await waitFor(() => {
        expect(mockOnAuditComplete).toHaveBeenCalled();
      });
    });

    it('should show progress during audit', async () => {
      const { getByText, getByTestId } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      expect(getByTestId('progress-bar')).toBeTruthy();
      expect(getByText('Auditing...')).toBeTruthy();
    });

    it('should call onIssueFound when issues are detected', async () => {
      const mockOnIssueFound = jest.fn();
      
      // Mock a failing contrast check
      mockAccessibilityUtils.meetsWCAGAA.mockReturnValue(false);
      mockAccessibilityUtils.getContrastRatio.mockReturnValue(2.0);

      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit 
            showDetailedReport={true}
            onIssueFound={mockOnIssueFound}
          />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(mockOnIssueFound).toHaveBeenCalledWith(
          expect.objectContaining({
            id: '1.4.3',
            criterion: 'Contrast (Minimum)',
            status: 'fail',
          })
        );
      });
    });
  });

  describe('Real-time Auditing', () => {
    it('should start real-time auditing when enabled', () => {
      jest.useFakeTimers();

      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit 
            enableRealTimeAudit={true}
            showDetailedReport={true}
          />
        </TestWrapper>
      );

      // Fast-forward time to trigger interval
      jest.advanceTimersByTime(5000);

      expect(getByText('Auditing...')).toBeTruthy();

      jest.useRealTimers();
    });

    it('should stop real-time auditing when component unmounts', () => {
      jest.useFakeTimers();
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');

      const { unmount } = render(
        <TestWrapper>
          <AccessibilityAudit 
            enableRealTimeAudit={true}
            showDetailedReport={true}
          />
        </TestWrapper>
      );

      unmount();

      expect(clearIntervalSpy).toHaveBeenCalled();

      jest.useRealTimers();
    });
  });

  describe('Audit Results Display', () => {
    it('should display audit results after completion', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText('1.1.1 - Non-text Content')).toBeTruthy();
        expect(getByText('1.4.3 - Contrast (Minimum)')).toBeTruthy();
        expect(getByText('2.1.1 - Keyboard')).toBeTruthy();
      });
    });

    it('should show pass status for passing criteria', async () => {
      const { getByText, getAllByTestId } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        const passIcons = getAllByTestId('pass-icon');
        expect(passIcons.length).toBeGreaterThan(0);
      });
    });

    it('should show fail status for failing criteria', async () => {
      // Mock failing contrast check
      mockAccessibilityUtils.meetsWCAGAA.mockReturnValue(false);
      mockAccessibilityUtils.getContrastRatio.mockReturnValue(2.0);

      const { getByText, getAllByTestId } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        const failIcons = getAllByTestId('fail-icon');
        expect(failIcons.length).toBeGreaterThan(0);
      });
    });

    it('should display impact badges correctly', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText('low')).toBeTruthy();
      });
    });

    it('should show recommendations for each criterion', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText(/Continue using semantic markup/)).toBeTruthy();
        expect(getByText(/Color contrast meets WCAG AA standards/)).toBeTruthy();
      });
    });
  });

  describe('Compliance Level Filtering', () => {
    it('should filter criteria based on compliance level A', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit 
            complianceLevel="A"
            showDetailedReport={true}
          />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText('1.1.1 - Non-text Content')).toBeTruthy();
        expect(getByText('2.1.1 - Keyboard')).toBeTruthy();
        // Should not show AA level criteria
        expect(() => getByText('1.4.3 - Contrast (Minimum)')).toThrow();
      });
    });

    it('should include all criteria for AAA compliance level', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit 
            complianceLevel="AAA"
            showDetailedReport={true}
          />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText('1.1.1 - Non-text Content')).toBeTruthy();
        expect(getByText('1.4.3 - Contrast (Minimum)')).toBeTruthy();
        expect(getByText('2.1.1 - Keyboard')).toBeTruthy();
      });
    });
  });

  describe('Color Contrast Testing', () => {
    it('should test color contrast correctly', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(mockAccessibilityUtils.getContrastRatio).toHaveBeenCalled();
        expect(mockAccessibilityUtils.meetsWCAGAA).toHaveBeenCalled();
      });
    });

    it('should display contrast ratio in results', async () => {
      mockAccessibilityUtils.getContrastRatio.mockReturnValue(4.51);

      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText(/Color contrast ratio: 4.51:1/)).toBeTruthy();
      });
    });
  });

  describe('Touch Target Testing', () => {
    it('should validate touch target sizes', async () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      await waitFor(() => {
        expect(getByText(/Touch targets meet minimum size of 44x44px/)).toBeTruthy();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility attributes', () => {
      const { getByRole } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByRole('button');
      expect(runButton.props.accessibilityLabel).toContain('Run Audit');
    });

    it('should be keyboard accessible', () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      expect(runButton.props.accessible).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle audit errors gracefully', async () => {
      // Mock an error in contrast checking
      mockAccessibilityUtils.getContrastRatio.mockImplementation(() => {
        throw new Error('Contrast calculation error');
      });

      const { getByText } = render(
        <TestWrapper>
          <AccessibilityAudit showDetailedReport={true} />
        </TestWrapper>
      );

      const runButton = getByText('Run Audit');
      fireEvent.press(runButton);

      // Should not crash and should complete audit
      await waitFor(() => {
        expect(getByText('Run Audit')).toBeTruthy();
      });
    });
  });
});
