/**
 * Enhanced Touch Target Test Suite
 * Comprehensive tests for the EnhancedTouchTarget component
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Text } from 'react-native';
import { EnhancedTouchTarget, AccessibleIconButton } from '../EnhancedTouchTarget';
import { AccessibilityUtils } from '../../../utils/accessibilityUtils';
import { ThemeProvider } from '../../../contexts/ThemeContext';

// Mock dependencies
jest.mock('../../../utils/accessibilityUtils');
jest.mock('../../../contexts/ThemeContext');

const mockAccessibilityUtils = AccessibilityUtils as jest.Mocked<typeof AccessibilityUtils>;

// Test wrapper
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    {children}
  </ThemeProvider>
);

describe('EnhancedTouchTarget', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockAccessibilityUtils.WCAG_STANDARDS = {
      TOUCH_TARGETS: {
        MINIMUM_SIZE: 44,
      },
    };
  });

  describe('Basic Rendering', () => {
    it('should render children correctly', () => {
      const { getByText } = render(
        <TestWrapper>
          <EnhancedTouchTarget>
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      expect(getByText('Touch me')).toBeTruthy();
    });

    it('should apply custom styles', () => {
      const customStyle = { backgroundColor: 'red' };

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget style={customStyle} testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      expect(touchTarget.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining(customStyle)
        ])
      );
    });

    it('should be disabled when disabled prop is true', () => {
      const mockOnPress = jest.fn();

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            disabled={true}
            onPress={mockOnPress}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent.press(touchTarget);

      expect(mockOnPress).not.toHaveBeenCalled();
    });
  });

  describe('Touch Target Size Enforcement', () => {
    it('should enforce minimum size when enforceMinimumSize is true', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            enforceMinimumSize={true}
            minimumSize={44}
            testID="touch-target"
          >
            <Text>Small content</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      expect(touchTarget.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            minWidth: 44,
            minHeight: 44,
          })
        ])
      );
    });

    it('should use custom minimum size', () => {
      const customMinSize = 60;

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            enforceMinimumSize={true}
            minimumSize={customMinSize}
            testID="touch-target"
          >
            <Text>Content</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      expect(touchTarget.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            minWidth: customMinSize,
            minHeight: customMinSize,
          })
        ])
      );
    });

    it('should not enforce minimum size when enforceMinimumSize is false', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            enforceMinimumSize={false}
            testID="touch-target"
          >
            <Text>Content</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      expect(touchTarget.props.style).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            minWidth: expect.any(Number),
            minHeight: expect.any(Number),
          })
        ])
      );
    });
  });

  describe('Touch Feedback', () => {
    it('should show touch feedback when enabled', async () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            showTouchFeedback={true}
            enableVisualFeedback={true}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'pressIn');

      await waitFor(() => {
        expect(getByTestId('touch-feedback')).toBeTruthy();
      });
    });

    it('should not show touch feedback when disabled', () => {
      const { getByTestId, queryByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            showTouchFeedback={false}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'pressIn');

      expect(queryByTestId('touch-feedback')).toBeFalsy();
    });

    it('should use custom feedback color', async () => {
      const customColor = '#FF0000';

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            showTouchFeedback={true}
            enableVisualFeedback={true}
            touchFeedbackColor={customColor}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'pressIn');

      await waitFor(() => {
        const feedback = getByTestId('touch-feedback');
        expect(feedback.props.style.backgroundColor).toBe(customColor);
      });
    });
  });

  describe('Press Handling', () => {
    it('should call onPress when pressed', () => {
      const mockOnPress = jest.fn();

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget onPress={mockOnPress} testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent.press(touchTarget);

      expect(mockOnPress).toHaveBeenCalledTimes(1);
    });

    it('should call onLongPress when long pressed', () => {
      const mockOnLongPress = jest.fn();

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget onLongPress={mockOnLongPress} testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'longPress');

      expect(mockOnLongPress).toHaveBeenCalledTimes(1);
    });

    it('should call onPressIn and onPressOut', () => {
      const mockOnPressIn = jest.fn();
      const mockOnPressOut = jest.fn();

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            onPressIn={mockOnPressIn}
            onPressOut={mockOnPressOut}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'pressIn');
      fireEvent(touchTarget, 'pressOut');

      expect(mockOnPressIn).toHaveBeenCalledTimes(1);
      expect(mockOnPressOut).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility attributes', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            accessibilityLabel="Test button"
            accessibilityHint="Performs test action"
            accessibilityRole="button"
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      expect(touchTarget.props.accessibilityLabel).toBe('Test button');
      expect(touchTarget.props.accessibilityHint).toBe('Performs test action');
      expect(touchTarget.props.accessibilityRole).toBe('button');
    });

    it('should be accessible by default', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      expect(touchTarget.props.accessible).toBe(true);
    });

    it('should handle focus states', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'focus');

      expect(touchTarget.props.style).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            borderWidth: 2,
          })
        ])
      );
    });
  });

  describe('Swipe Gestures', () => {
    it('should handle swipe gestures when enabled', () => {
      const mockOnSwipeLeft = jest.fn();
      const mockOnSwipeRight = jest.fn();

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            enableSwipeGestures={true}
            onSwipeLeft={mockOnSwipeLeft}
            onSwipeRight={mockOnSwipeRight}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      
      // Simulate swipe left
      fireEvent(touchTarget, 'panResponderRelease', {}, { dx: -60, dy: 0 });
      expect(mockOnSwipeLeft).toHaveBeenCalledTimes(1);

      // Simulate swipe right
      fireEvent(touchTarget, 'panResponderRelease', {}, { dx: 60, dy: 0 });
      expect(mockOnSwipeRight).toHaveBeenCalledTimes(1);
    });

    it('should not handle swipe gestures when disabled', () => {
      const mockOnSwipeLeft = jest.fn();

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget 
            enableSwipeGestures={false}
            onSwipeLeft={mockOnSwipeLeft}
            testID="touch-target"
          >
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      const touchTarget = getByTestId('touch-target');
      fireEvent(touchTarget, 'panResponderRelease', {}, { dx: -60, dy: 0 });

      expect(mockOnSwipeLeft).not.toHaveBeenCalled();
    });
  });

  describe('Auto Focus', () => {
    it('should auto focus when autoFocus is true', async () => {
      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget autoFocus={true} testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      await waitFor(() => {
        const touchTarget = getByTestId('touch-target');
        expect(touchTarget.props.style).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              borderWidth: 2,
            })
          ])
        );
      });
    });
  });

  describe('Development Mode', () => {
    it('should show size indicator in development mode', () => {
      // Mock __DEV__ to be true
      (global as any).__DEV__ = true;

      const { getByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget enforceMinimumSize={true} testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      expect(getByTestId('size-indicator')).toBeTruthy();
    });

    it('should not show size indicator in production mode', () => {
      // Mock __DEV__ to be false
      (global as any).__DEV__ = false;

      const { queryByTestId } = render(
        <TestWrapper>
          <EnhancedTouchTarget enforceMinimumSize={true} testID="touch-target">
            <Text>Touch me</Text>
          </EnhancedTouchTarget>
        </TestWrapper>
      );

      expect(queryByTestId('size-indicator')).toBeFalsy();
    });
  });
});

describe('AccessibleIconButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render icon and label correctly', () => {
      const { getByText, getByTestId } = render(
        <TestWrapper>
          <AccessibleIconButton 
            iconName="home"
            label="Home"
            showLabel={true}
            testID="icon-button"
          />
        </TestWrapper>
      );

      expect(getByText('Home')).toBeTruthy();
      expect(getByTestId('icon-button')).toBeTruthy();
    });

    it('should not show label when showLabel is false', () => {
      const { queryByText } = render(
        <TestWrapper>
          <AccessibleIconButton 
            iconName="home"
            label="Home"
            showLabel={false}
          />
        </TestWrapper>
      );

      expect(queryByText('Home')).toBeFalsy();
    });

    it('should position label correctly', () => {
      const { getByText } = render(
        <TestWrapper>
          <AccessibleIconButton 
            iconName="home"
            label="Home"
            showLabel={true}
            labelPosition="right"
          />
        </TestWrapper>
      );

      const label = getByText('Home');
      expect(label.props.style.marginLeft).toBe(4);
    });
  });

  describe('Accessibility', () => {
    it('should use label as accessibility label', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <AccessibleIconButton 
            iconName="home"
            label="Home"
            testID="icon-button"
          />
        </TestWrapper>
      );

      const button = getByTestId('icon-button');
      expect(button.props.accessibilityLabel).toBe('Home');
    });

    it('should use custom accessibility label when provided', () => {
      const { getByTestId } = render(
        <TestWrapper>
          <AccessibleIconButton 
            iconName="home"
            label="Home"
            accessibilityLabel="Navigate to home screen"
            testID="icon-button"
          />
        </TestWrapper>
      );

      const button = getByTestId('icon-button');
      expect(button.props.accessibilityLabel).toBe('Navigate to home screen');
    });
  });
});
