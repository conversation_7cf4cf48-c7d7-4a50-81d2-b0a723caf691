/**
 * Enhanced Error Message Component - User-Friendly Error Display
 *
 * Component Contract:
 * - Displays user-friendly error messages with Aura design system
 * - Provides actionable error recovery options
 * - Supports different error types and severity levels
 * - Implements accessibility features for screen readers
 * - Includes retry functionality and error reporting
 *
 * @version 3.0.0 - Enhanced with Aura Design System
 * <AUTHOR> Development Team
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { EnhancedTouchTarget } from '../ui/EnhancedTouchTarget';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsive';

export interface ErrorMessageProps {
  error?: Error | string | null;
  title?: string;
  message?: string;
  type?: 'error' | 'warning' | 'info';
  showRetry?: boolean;
  onRetry?: () => void;
  showReport?: boolean;
  onReport?: () => void;
  style?: any;
  testID?: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  title,
  message,
  type = 'error',
  showRetry = false,
  onRetry,
  showReport = false,
  onReport,
  style,
  testID = 'error-message',
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors, type);

  // Don't render if no error
  if (!error && !message) {
    return null;
  }

  const errorMessage = message || (typeof error === 'string' ? error : error?.message) || 'An unexpected error occurred';
  const errorTitle = title || getDefaultTitle(type);
  const iconName = getIconName(type);
  const iconColor = getIconColor(type, colors);

  return (
    <View style={[styles.container, style]} testID={testID}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name={iconName} size={24} color={iconColor} />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={styles.title} accessibilityRole="text">
            {errorTitle}
          </Text>
          <Text style={styles.message} accessibilityRole="text">
            {errorMessage}
          </Text>
        </View>
      </View>

      {(showRetry || showReport) && (
        <View style={styles.actions}>
          {showRetry && onRetry && (
            <EnhancedTouchTarget
              style={styles.retryButton}
              onPress={onRetry}
              accessibilityLabel="Try again"
              accessibilityHint="Retry the failed operation"
              testID={`${testID}-retry-button`}
              minimumSize={44}
              showTouchFeedback={true}
            >
              <Ionicons name="refresh" size={16} color={colors.primary?.default} />
              <Text style={styles.retryButtonText}>Try Again</Text>
            </EnhancedTouchTarget>
          )}

          {showReport && onReport && (
            <EnhancedTouchTarget
              style={styles.reportButton}
              onPress={onReport}
              accessibilityLabel="Report issue"
              accessibilityHint="Report this error to help us improve"
              testID={`${testID}-report-button`}
              minimumSize={44}
              showTouchFeedback={true}
            >
              <Ionicons name="bug" size={16} color={colors.text?.secondary} />
              <Text style={styles.reportButtonText}>Report</Text>
            </EnhancedTouchTarget>
          )}
        </View>
      )}
    </View>
  );
};

// Helper functions
const getDefaultTitle = (type: 'error' | 'warning' | 'info'): string => {
  switch (type) {
    case 'error':
      return 'Something went wrong';
    case 'warning':
      return 'Warning';
    case 'info':
      return 'Information';
    default:
      return 'Notice';
  }
};

const getIconName = (type: 'error' | 'warning' | 'info'): any => {
  switch (type) {
    case 'error':
      return 'alert-circle';
    case 'warning':
      return 'warning';
    case 'info':
      return 'information-circle';
    default:
      return 'alert-circle';
  }
};

const getIconColor = (type: 'error' | 'warning' | 'info', colors: any): string => {
  switch (type) {
    case 'error':
      return colors.error?.default || '#DC2626';
    case 'warning':
      return colors.warning?.default || '#F59E0B';
    case 'info':
      return colors.info?.default || '#3B82F6';
    default:
      return colors.error?.default || '#DC2626';
  }
};

const createStyles = (colors: any, type: 'error' | 'warning' | 'info') => {
  const backgroundColor = (() => {
    switch (type) {
      case 'error':
        return colors.error?.light || '#FEF2F2';
      case 'warning':
        return colors.warning?.light || '#FFFBEB';
      case 'info':
        return colors.info?.light || '#EFF6FF';
      default:
        return colors.error?.light || '#FEF2F2';
    }
  })();

  const borderColor = (() => {
    switch (type) {
      case 'error':
        return colors.error?.default || '#DC2626';
      case 'warning':
        return colors.warning?.default || '#F59E0B';
      case 'info':
        return colors.info?.default || '#3B82F6';
      default:
        return colors.error?.default || '#DC2626';
    }
  })();

  return StyleSheet.create({
    container: {
      backgroundColor,
      borderWidth: 1,
      borderColor,
      borderRadius: 12,
      padding: getResponsiveSpacing(4),
      marginVertical: getResponsiveSpacing(2),
    },
    content: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    iconContainer: {
      marginRight: getResponsiveSpacing(3),
      marginTop: 2,
    },
    textContainer: {
      flex: 1,
    },
    title: {
      fontSize: getResponsiveFontSize(16),
      fontWeight: '600',
      color: colors.text?.primary || '#1F2937',
      marginBottom: getResponsiveSpacing(1),
    },
    message: {
      fontSize: getResponsiveFontSize(14),
      color: colors.text?.secondary || '#6B7280',
      lineHeight: 20,
    },
    actions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: getResponsiveSpacing(3),
      gap: getResponsiveSpacing(2),
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary?.light || '#E0F2FE',
      borderRadius: 8,
      paddingHorizontal: getResponsiveSpacing(3),
      paddingVertical: getResponsiveSpacing(2),
      gap: getResponsiveSpacing(1),
    },
    retryButtonText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.primary?.default || '#0369A1',
    },
    reportButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background?.secondary || '#F9FAFB',
      borderRadius: 8,
      paddingHorizontal: getResponsiveSpacing(3),
      paddingVertical: getResponsiveSpacing(2),
      gap: getResponsiveSpacing(1),
    },
    reportButtonText: {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '500',
      color: colors.text?.secondary || '#6B7280',
    },
  });
};
