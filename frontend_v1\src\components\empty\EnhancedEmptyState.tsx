/**
 * Enhanced Empty State Component
 * Provides comprehensive empty state UI with contextual actions
 */

import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  ViewStyle, 
  TextStyle,
  Image,
  ImageSourcePropType 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';

export interface EmptyStateProps {
  icon?: string;
  image?: ImageSourcePropType;
  title: string;
  description?: string;
  primaryAction?: {
    label: string;
    onPress: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  secondaryAction?: {
    label: string;
    onPress: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  variant?: 'default' | 'search' | 'error' | 'offline' | 'maintenance';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  testID?: string;
  showAnimation?: boolean;
  customContent?: React.ReactNode;
}

interface EmptyStateConfig {
  iconColor: string;
  titleColor: string;
  descriptionColor: string;
  backgroundColor: string;
  defaultIcon: string;
}

const getEmptyStateConfig = (variant: EmptyStateProps['variant'], colors: any): EmptyStateConfig => {
  switch (variant) {
    case 'search':
      return {
        iconColor: colors.text.secondary,
        titleColor: colors.text.primary,
        descriptionColor: colors.text.secondary,
        backgroundColor: colors.background.primary,
        defaultIcon: 'search-outline',
      };
    case 'error':
      return {
        iconColor: '#FF6B6B',
        titleColor: '#FF6B6B',
        descriptionColor: colors.text.secondary,
        backgroundColor: colors.background.primary,
        defaultIcon: 'alert-circle-outline',
      };
    case 'offline':
      return {
        iconColor: '#FFA726',
        titleColor: colors.text.primary,
        descriptionColor: colors.text.secondary,
        backgroundColor: colors.background.primary,
        defaultIcon: 'cloud-offline-outline',
      };
    case 'maintenance':
      return {
        iconColor: '#42A5F5',
        titleColor: colors.text.primary,
        descriptionColor: colors.text.secondary,
        backgroundColor: colors.background.primary,
        defaultIcon: 'construct-outline',
      };
    case 'default':
    default:
      return {
        iconColor: colors.text.secondary,
        titleColor: colors.text.primary,
        descriptionColor: colors.text.secondary,
        backgroundColor: colors.background.primary,
        defaultIcon: 'document-outline',
      };
  }
};

export const EnhancedEmptyState: React.FC<EmptyStateProps> = ({
  icon,
  image,
  title,
  description,
  primaryAction,
  secondaryAction,
  variant = 'default',
  size = 'medium',
  style,
  testID = 'empty-state',
  showAnimation = true,
  customContent,
}) => {
  const { colors } = useTheme();
  const config = getEmptyStateConfig(variant, colors);

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 40;
      case 'large':
        return 80;
      default:
        return 60;
    }
  };

  const getImageSize = () => {
    switch (size) {
      case 'small':
        return { width: 80, height: 80 };
      case 'large':
        return { width: 160, height: 160 };
      default:
        return { width: 120, height: 120 };
    }
  };

  const getTitleStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      color: config.titleColor,
      fontWeight: '600',
      textAlign: 'center',
      marginBottom: getResponsiveSpacing(2),
    };

    switch (size) {
      case 'small':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(16),
        };
      case 'large':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(24),
        };
      default:
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(20),
        };
    }
  };

  const getDescriptionStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      color: config.descriptionColor,
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: getResponsiveSpacing(4),
    };

    switch (size) {
      case 'small':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(12),
        };
      case 'large':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(16),
        };
      default:
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(14),
        };
    }
  };

  const getButtonStyle = (buttonVariant: 'primary' | 'secondary' | 'outline' = 'primary'): ViewStyle => {
    const baseStyle: ViewStyle = {
      paddingHorizontal: getResponsiveSpacing(4),
      paddingVertical: getResponsiveSpacing(2),
      borderRadius: 8,
      marginHorizontal: getResponsiveSpacing(1),
      minWidth: 120,
    };

    switch (buttonVariant) {
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: colors.background.secondary,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.primary.default,
        };
      case 'primary':
      default:
        return {
          ...baseStyle,
          backgroundColor: colors.primary.default,
        };
    }
  };

  const getButtonTextStyle = (buttonVariant: 'primary' | 'secondary' | 'outline' = 'primary'): TextStyle => {
    const baseStyle: TextStyle = {
      fontSize: getResponsiveFontSize(14),
      fontWeight: '600',
      textAlign: 'center',
    };

    switch (buttonVariant) {
      case 'secondary':
        return {
          ...baseStyle,
          color: colors.text.primary,
        };
      case 'outline':
        return {
          ...baseStyle,
          color: colors.primary.default,
        };
      case 'primary':
      default:
        return {
          ...baseStyle,
          color: '#FFFFFF',
        };
    }
  };

  const renderIcon = () => {
    if (image) {
      return (
        <Image
          source={image}
          style={[styles.image, getImageSize()]}
          resizeMode="contain"
        />
      );
    }

    return (
      <Ionicons
        name={(icon || config.defaultIcon) as any}
        size={getIconSize()}
        color={config.iconColor}
        style={styles.icon}
      />
    );
  };

  const renderActions = () => {
    if (!primaryAction && !secondaryAction) return null;

    return (
      <View style={styles.actionsContainer}>
        {primaryAction && (
          <TouchableOpacity
            style={getButtonStyle(primaryAction.variant)}
            onPress={primaryAction.onPress}
            testID={`${testID}-primary-action`}
            accessibilityRole="button"
            accessibilityLabel={primaryAction.label}
          >
            <Text style={getButtonTextStyle(primaryAction.variant)}>
              {primaryAction.label}
            </Text>
          </TouchableOpacity>
        )}

        {secondaryAction && (
          <TouchableOpacity
            style={getButtonStyle(secondaryAction.variant)}
            onPress={secondaryAction.onPress}
            testID={`${testID}-secondary-action`}
            accessibilityRole="button"
            accessibilityLabel={secondaryAction.label}
          >
            <Text style={getButtonTextStyle(secondaryAction.variant)}>
              {secondaryAction.label}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: config.backgroundColor },
        style,
      ]}
      testID={testID}
      accessibilityRole="text"
      accessibilityLabel={`Empty state: ${title}${description ? `. ${description}` : ''}`}
    >
      <View style={styles.content}>
        {renderIcon()}

        <Text style={getTitleStyle()}>
          {title}
        </Text>

        {description && (
          <Text style={getDescriptionStyle()}>
            {description}
          </Text>
        )}

        {customContent}

        {renderActions()}
      </View>
    </View>
  );
};

// Convenience components for common empty states
export const SearchEmptyState: React.FC<Omit<EmptyStateProps, 'variant'>> = (props) => (
  <EnhancedEmptyState {...props} variant="search" />
);

export const ErrorEmptyState: React.FC<Omit<EmptyStateProps, 'variant'>> = (props) => (
  <EnhancedEmptyState {...props} variant="error" />
);

export const OfflineEmptyState: React.FC<Omit<EmptyStateProps, 'variant'>> = (props) => (
  <EnhancedEmptyState {...props} variant="offline" />
);

export const MaintenanceEmptyState: React.FC<Omit<EmptyStateProps, 'variant'>> = (props) => (
  <EnhancedEmptyState {...props} variant="maintenance" />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(6),
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
    width: '100%',
  },
  icon: {
    marginBottom: getResponsiveSpacing(3),
  },
  image: {
    marginBottom: getResponsiveSpacing(3),
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: getResponsiveSpacing(2),
  },
});
