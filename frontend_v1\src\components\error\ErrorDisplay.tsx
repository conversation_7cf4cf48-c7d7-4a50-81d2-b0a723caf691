/**
 * Enhanced Error Display Component
 * Provides consistent error UI across the application
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';

export interface ErrorDisplayProps {
  error: string | Error | null;
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  variant?: 'inline' | 'card' | 'banner' | 'modal' | 'fullscreen';
  severity?: 'error' | 'warning' | 'info';
  style?: ViewStyle;
  showIcon?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
  testID?: string;
  retryCount?: number;
  maxRetries?: number;
  showRetryCount?: boolean;
  enableHaptics?: boolean;
}

interface ErrorConfig {
  icon: string;
  color: string;
  backgroundColor: string;
  borderColor: string;
}

const getErrorConfig = (severity: 'error' | 'warning' | 'info'): ErrorConfig => {
  switch (severity) {
    case 'error':
      return {
        icon: 'alert-circle',
        color: '#FF6B6B',
        backgroundColor: '#FFF5F5',
        borderColor: '#FFE0E0',
      };
    case 'warning':
      return {
        icon: 'warning',
        color: '#FFA726',
        backgroundColor: '#FFF8E1',
        borderColor: '#FFE0B2',
      };
    case 'info':
      return {
        icon: 'information-circle',
        color: '#42A5F5',
        backgroundColor: '#E3F2FD',
        borderColor: '#BBDEFB',
      };
    default:
      return {
        icon: 'alert-circle',
        color: '#FF6B6B',
        backgroundColor: '#FFF5F5',
        borderColor: '#FFE0E0',
      };
  }
};

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  title,
  description,
  actionLabel = 'Try Again',
  onAction,
  variant = 'card',
  severity = 'error',
  style,
  showIcon = true,
  dismissible = false,
  onDismiss,
  testID = 'error-display',
  retryCount = 0,
  maxRetries = 3,
  showRetryCount = false,
  enableHaptics = true,
}) => {
  const { colors } = useTheme();

  if (!error) return null;

  const errorMessage = error instanceof Error ? error.message : error;
  const config = getErrorConfig(severity);

  const getVariantStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
    };

    switch (variant) {
      case 'inline':
        return {
          ...baseStyle,
          flexDirection: 'row',
          alignItems: 'center',
          padding: getResponsiveSpacing(2),
          borderRadius: 6,
          borderWidth: 1,
        };
      case 'banner':
        return {
          ...baseStyle,
          flexDirection: 'row',
          alignItems: 'center',
          padding: getResponsiveSpacing(3),
          borderBottomWidth: 1,
        };
      case 'modal':
        return {
          ...baseStyle,
          padding: getResponsiveSpacing(4),
          borderRadius: 12,
          borderWidth: 1,
          margin: getResponsiveSpacing(4),
        };
      case 'fullscreen':
        return {
          ...baseStyle,
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: getResponsiveSpacing(6),
        };
      case 'card':
      default:
        return {
          ...baseStyle,
          padding: getResponsiveSpacing(4),
          borderRadius: 8,
          borderWidth: 1,
          margin: getResponsiveSpacing(2),
        };
    }
  };

  const getIconSize = () => {
    switch (variant) {
      case 'inline':
        return 20;
      case 'banner':
        return 24;
      case 'fullscreen':
        return 48;
      default:
        return 28;
    }
  };

  const getTitleStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      color: config.color,
      fontWeight: '600',
      marginBottom: getResponsiveSpacing(1),
    };

    switch (variant) {
      case 'inline':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(14),
          marginBottom: 0,
          marginLeft: getResponsiveSpacing(2),
        };
      case 'banner':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(16),
          marginLeft: getResponsiveSpacing(2),
        };
      case 'fullscreen':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(20),
          textAlign: 'center',
          marginTop: getResponsiveSpacing(3),
        };
      default:
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(16),
        };
    }
  };

  const getMessageStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      color: colors.text.secondary,
      lineHeight: 20,
    };

    switch (variant) {
      case 'inline':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(12),
          marginLeft: getResponsiveSpacing(2),
          flex: 1,
        };
      case 'banner':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(14),
          marginLeft: getResponsiveSpacing(2),
          flex: 1,
        };
      case 'fullscreen':
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(16),
          textAlign: 'center',
          marginBottom: getResponsiveSpacing(4),
        };
      default:
        return {
          ...baseStyle,
          fontSize: getResponsiveFontSize(14),
          marginBottom: getResponsiveSpacing(3),
        };
    }
  };

  const handleAction = () => {
    if (enableHaptics) {
      // Add haptic feedback here if available
    }
    onAction?.();
  };

  const handleDismiss = () => {
    if (enableHaptics) {
      // Add haptic feedback here if available
    }
    onDismiss?.();
  };

  const renderContent = () => {
    const isInlineVariant = variant === 'inline' || variant === 'banner';
    
    return (
      <>
        {showIcon && (
          <Ionicons
            name={config.icon as any}
            size={getIconSize()}
            color={config.color}
            style={isInlineVariant ? undefined : styles.icon}
          />
        )}
        
        <View style={isInlineVariant ? styles.inlineContent : styles.content}>
          {title && (
            <Text style={getTitleStyle()}>
              {title}
            </Text>
          )}
          
          <Text style={getMessageStyle()}>
            {description || errorMessage}
          </Text>
          
          {showRetryCount && retryCount > 0 && (
            <Text style={[styles.retryText, { color: config.color }]}>
              Attempt {retryCount} of {maxRetries}
            </Text>
          )}
        </View>

        {(onAction || dismissible) && (
          <View style={styles.actions}>
            {onAction && retryCount < maxRetries && (
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: config.color }]}
                onPress={handleAction}
                testID={`${testID}-action`}
              >
                <Text style={styles.actionButtonText}>
                  {actionLabel}
                </Text>
              </TouchableOpacity>
            )}
            
            {dismissible && (
              <TouchableOpacity
                style={styles.dismissButton}
                onPress={handleDismiss}
                testID={`${testID}-dismiss`}
              >
                <Ionicons name="close" size={20} color={colors.text.secondary} />
              </TouchableOpacity>
            )}
          </View>
        )}
      </>
    );
  };

  return (
    <View 
      style={[getVariantStyles(), style]} 
      testID={testID}
      accessibilityRole="alert"
      accessibilityLabel={`${severity} message: ${errorMessage}`}
    >
      {variant === 'inline' || variant === 'banner' ? (
        renderContent()
      ) : (
        <View style={variant === 'fullscreen' ? styles.fullscreenContent : undefined}>
          {renderContent()}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  icon: {
    alignSelf: 'center',
    marginBottom: 8,
  },
  content: {
    flex: 1,
  },
  inlineContent: {
    flex: 1,
    marginLeft: 8,
  },
  fullscreenContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    flex: 1,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  dismissButton: {
    padding: 4,
  },
  retryText: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: 4,
  },
});
