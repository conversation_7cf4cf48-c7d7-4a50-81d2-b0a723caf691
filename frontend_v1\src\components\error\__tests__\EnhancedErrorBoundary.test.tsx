/**
 * Enhanced Error Boundary Test Suite
 * Comprehensive tests for the EnhancedErrorBoundary component
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { EnhancedErrorBoundary } from '../EnhancedErrorBoundary';
import { performanceMonitor } from '../../../services/performanceMonitor';
import { errorHandler } from '../../../utils/errorHandler';

// Mock dependencies
jest.mock('../../../services/performanceMonitor');
jest.mock('../../../utils/errorHandler');

// Test component that throws an error
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('EnhancedErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.error for error boundary tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Normal Operation', () => {
    it('should render children when no error occurs', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary>
          <ThrowError shouldThrow={false} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('No error')).toBeTruthy();
    });

    it('should not call error handlers when no error occurs', () => {
      render(
        <EnhancedErrorBoundary>
          <ThrowError shouldThrow={false} />
        </EnhancedErrorBoundary>
      );

      expect(errorHandler.handleError).not.toHaveBeenCalled();
      expect(performanceMonitor.trackError).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should catch and display error when child component throws', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Something went wrong')).toBeTruthy();
      expect(getByText('Test error')).toBeTruthy();
    });

    it('should call error handler when error occurs', () => {
      render(
        <EnhancedErrorBoundary>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(errorHandler.handleError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          component: 'EnhancedErrorBoundary',
          errorInfo: expect.any(Object),
          errorId: expect.any(String),
        })
      );
    });

    it('should track error in performance monitor', () => {
      render(
        <EnhancedErrorBoundary>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(performanceMonitor.trackError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          component: 'EnhancedErrorBoundary',
          errorId: expect.any(String),
        })
      );
    });

    it('should call custom onError callback when provided', () => {
      const mockOnError = jest.fn();

      render(
        <EnhancedErrorBoundary onError={mockOnError}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(mockOnError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.any(Object)
      );
    });
  });

  describe('Retry Functionality', () => {
    it('should show retry button when enableRetry is true', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary enableRetry={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText(/Try Again/)).toBeTruthy();
    });

    it('should not show retry button when enableRetry is false', () => {
      const { queryByText } = render(
        <EnhancedErrorBoundary enableRetry={false}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(queryByText(/Try Again/)).toBeFalsy();
    });

    it('should reset error state when retry button is pressed', async () => {
      const { getByText, queryByText, rerender } = render(
        <EnhancedErrorBoundary enableRetry={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Something went wrong')).toBeTruthy();

      const retryButton = getByText(/Try Again/);
      fireEvent.press(retryButton);

      // Re-render with no error
      rerender(
        <EnhancedErrorBoundary enableRetry={true}>
          <ThrowError shouldThrow={false} />
        </EnhancedErrorBoundary>
      );

      await waitFor(() => {
        expect(queryByText('Something went wrong')).toBeFalsy();
        expect(getByText('No error')).toBeTruthy();
      });
    });

    it('should track retry attempts in performance monitor', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary enableRetry={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      const retryButton = getByText(/Try Again/);
      fireEvent.press(retryButton);

      expect(performanceMonitor.trackUserInteraction).toHaveBeenCalledWith(
        'error_boundary_retry',
        0,
        expect.objectContaining({
          retryCount: expect.any(Number),
          errorId: expect.any(String),
        })
      );
    });

    it('should show retry count when maxRetries is set', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary enableRetry={true} maxRetries={3}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText(/Try Again \(0\/3\)/)).toBeTruthy();
    });

    it('should disable retry button after max retries reached', () => {
      const { getByText, queryByText } = render(
        <EnhancedErrorBoundary enableRetry={true} maxRetries={1}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      const retryButton = getByText(/Try Again/);
      fireEvent.press(retryButton);

      expect(queryByText(/Try Again/)).toBeFalsy();
      expect(getByText(/Maximum retry attempts reached/)).toBeTruthy();
    });
  });

  describe('Critical Error Handling', () => {
    it('should display critical error styling when criticalError is true', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary criticalError={true} screenName="Test Screen">
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Test Screen Error')).toBeTruthy();
    });

    it('should show report issue button for critical errors', () => {
      const { getByText } = render(
        <EnhancedErrorBoundary criticalError={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Report Issue')).toBeTruthy();
    });
  });

  describe('Error Reporting', () => {
    it('should report error when enableReporting is true', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      render(
        <EnhancedErrorBoundary enableReporting={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        '🚨 Error Boundary Report:',
        expect.objectContaining({
          errorId: expect.any(String),
          message: 'Test error',
          timestamp: expect.any(String),
        })
      );
    });

    it('should not report error when enableReporting is false', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      render(
        <EnhancedErrorBoundary enableReporting={false}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(consoleSpy).not.toHaveBeenCalledWith(
        '🚨 Error Boundary Report:',
        expect.any(Object)
      );
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility attributes', () => {
      const { getByRole } = render(
        <EnhancedErrorBoundary>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      const errorContainer = getByRole('alert');
      expect(errorContainer).toBeTruthy();
    });

    it('should have accessible retry button', () => {
      const { getByRole } = render(
        <EnhancedErrorBoundary enableRetry={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      const retryButton = getByRole('button');
      expect(retryButton.props.accessibilityLabel).toContain('Try Again');
    });
  });

  describe('Custom Fallback', () => {
    it('should render custom fallback when provided', () => {
      const CustomFallback = () => <div>Custom Error UI</div>;

      const { getByText } = render(
        <EnhancedErrorBoundary fallback={<CustomFallback />}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Custom Error UI')).toBeTruthy();
    });
  });

  describe('Reset on Props Change', () => {
    it('should reset error when resetKeys change', () => {
      const { getByText, queryByText, rerender } = render(
        <EnhancedErrorBoundary resetKeys={['key1']}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Something went wrong')).toBeTruthy();

      rerender(
        <EnhancedErrorBoundary resetKeys={['key2']}>
          <ThrowError shouldThrow={false} />
        </EnhancedErrorBoundary>
      );

      expect(queryByText('Something went wrong')).toBeFalsy();
      expect(getByText('No error')).toBeTruthy();
    });

    it('should reset error when resetOnPropsChange is true and children change', () => {
      const { getByText, queryByText, rerender } = render(
        <EnhancedErrorBoundary resetOnPropsChange={true}>
          <ThrowError shouldThrow={true} />
        </EnhancedErrorBoundary>
      );

      expect(getByText('Something went wrong')).toBeTruthy();

      rerender(
        <EnhancedErrorBoundary resetOnPropsChange={true}>
          <div>Different child</div>
        </EnhancedErrorBoundary>
      );

      expect(queryByText('Something went wrong')).toBeFalsy();
      expect(getByText('Different child')).toBeTruthy();
    });
  });
});
