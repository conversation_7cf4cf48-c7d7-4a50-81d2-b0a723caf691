/**
 * Enhanced Error Handling Components
 * Centralized exports for all error handling components
 */

// Error Boundaries
export { EnhancedErrorBoundary } from './EnhancedErrorBoundary';
export { ErrorBoundary } from './ErrorBoundary';

// Error Display Components
export { ErrorDisplay } from './ErrorDisplay';
export type { ErrorDisplayProps } from './ErrorDisplay';

// Fallback Components
export { ErrorFallback } from './ErrorFallback';
export type { ErrorFallbackType } from './ErrorFallback';

export { DataLoadingFallback } from './DataLoadingFallback';
export type { DataLoadingFallbackProps } from './DataLoadingFallback';

export { NetworkErrorFallback } from './NetworkErrorFallback';

// Loading States
export { 
  EnhancedLoadingState,
  LoadingSpinner,
  LoadingDots,
  LoadingSkeleton,
  LoadingProgress,
  LoadingPulse,
  LoadingOverlay
} from '../loading/EnhancedLoadingStates';
export type { LoadingStateProps } from '../loading/EnhancedLoadingStates';

// Empty States
export {
  EnhancedEmptyState,
  SearchEmptyState,
  ErrorEmptyState,
  OfflineEmptyState,
  MaintenanceEmptyState
} from '../empty/EnhancedEmptyState';
export type { EmptyStateProps } from '../empty/EnhancedEmptyState';

// User Feedback
export { EnhancedUserFeedback } from '../feedback/EnhancedUserFeedback';
export type { FeedbackMessage, FeedbackAction } from '../feedback/EnhancedUserFeedback';

// Hooks
export { useErrorRecovery } from '../../hooks/useErrorRecovery';
export type { 
  ErrorRecoveryOptions,
  ErrorRecoveryState,
  ErrorRecoveryActions,
  UseErrorRecoveryResult
} from '../../hooks/useErrorRecovery';

export { useErrorHandler } from '../../hooks/useErrorHandler';
export { useErrorHandling } from '../../hooks/useErrorHandling';

// Services
export { 
  enhancedErrorHandlingService,
  handleError,
  addRecoveryStrategy,
  addErrorListener
} from '../../services/enhancedErrorHandlingService';
export type {
  ErrorHandlingConfig,
  ErrorContext,
  ErrorReport,
  RecoveryStrategy
} from '../../services/enhancedErrorHandlingService';

// Utilities
export { errorHandler } from '../../utils/errorHandler';
export { constructiveErrorHandler } from '../../utils/constructiveErrorHandler';
export type { ErrorContext as ConstructiveErrorContext } from '../../utils/constructiveErrorHandler';

// Constants
export { 
  VALIDATION_ERRORS,
  NETWORK_ERRORS,
  BOOKING_ERRORS,
  SYSTEM_ERRORS
} from '../../constants/errorMessages';
