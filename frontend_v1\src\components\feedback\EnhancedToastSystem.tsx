/**
 * Enhanced Toast System - User Feedback with Aura Design System
 *
 * Component Contract:
 * - Provides comprehensive toast notifications with Aura design
 * - Supports multiple toast types (success, error, warning, info)
 * - Implements accessibility features and screen reader support
 * - Includes haptic feedback and smooth animations
 * - Manages toast queue and auto-dismissal
 * - Supports custom actions and interactive toasts
 *
 * @version 3.0.0 - Enhanced with Aura Design System
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  Animated,
  PanGestureHandler,
  State as GestureState,
  StyleSheet,
  Dimensions,
  AccessibilityInfo,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

// Enhanced UI Components
import { EnhancedTouchTarget } from '../ui/EnhancedTouchTarget';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsive';
import { useTheme } from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface ToastConfig {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onPress: () => void;
  };
  onDismiss?: () => void;
  enableHaptics?: boolean;
  accessibilityAnnouncement?: string;
}

interface ToastSystemProps {
  position?: 'top' | 'bottom';
  maxToasts?: number;
  defaultDuration?: number;
}

export const EnhancedToastSystem: React.FC<ToastSystemProps> = ({
  position = 'top',
  maxToasts = 3,
  defaultDuration = 4000,
}) => {
  const { colors } = useTheme();
  const [toasts, setToasts] = useState<ToastConfig[]>([]);
  const styles = createStyles(colors, position);

  const showToast = useCallback((config: Omit<ToastConfig, 'id'>) => {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const toast: ToastConfig = {
      ...config,
      id,
      duration: config.duration ?? defaultDuration,
    };

    setToasts(prev => {
      const newToasts = [toast, ...prev];
      return newToasts.slice(0, maxToasts);
    });

    // Haptic feedback
    if (config.enableHaptics !== false) {
      switch (config.type) {
        case 'success':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'error':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
        case 'warning':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        default:
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }

    // Screen reader announcement
    const announcement = config.accessibilityAnnouncement || 
      `${config.type} notification: ${config.title}${config.message ? `. ${config.message}` : ''}`;
    AccessibilityInfo.announceForAccessibility(announcement);

    // Auto dismiss
    if (!config.persistent && config.duration && config.duration > 0) {
      setTimeout(() => {
        dismissToast(id);
      }, config.duration);
    }
  }, [defaultDuration, maxToasts]);

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => {
      const toast = prev.find(t => t.id === id);
      if (toast?.onDismiss) {
        toast.onDismiss();
      }
      return prev.filter(t => t.id !== id);
    });
  }, []);

  const dismissAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return (
    <View style={styles.container} pointerEvents="box-none">
      {toasts.map((toast, index) => (
        <ToastItem
          key={toast.id}
          toast={toast}
          index={index}
          onDismiss={() => dismissToast(toast.id)}
          position={position}
        />
      ))}
    </View>
  );
};

interface ToastItemProps {
  toast: ToastConfig;
  index: number;
  onDismiss: () => void;
  position: 'top' | 'bottom';
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, index, onDismiss, position }) => {
  const { colors } = useTheme();
  const translateY = useRef(new Animated.Value(position === 'top' ? -100 : 100)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.9)).current;
  const styles = createStyles(colors, position);

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
    ]).start();
  }, []);

  const handleDismiss = useCallback(() => {
    // Exit animation
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -100 : 100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 0.9,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss();
    });
  }, [onDismiss, position]);

  const getToastColors = () => {
    switch (toast.type) {
      case 'success':
        return {
          gradient: [colors.success?.default || '#10B981', colors.success?.light || '#34D399'],
          icon: 'checkmark-circle',
        };
      case 'error':
        return {
          gradient: [colors.error?.default || '#DC2626', colors.error?.light || '#F87171'],
          icon: 'alert-circle',
        };
      case 'warning':
        return {
          gradient: [colors.warning?.default || '#F59E0B', colors.warning?.light || '#FBBF24'],
          icon: 'warning',
        };
      case 'info':
      default:
        return {
          gradient: [colors.info?.default || '#3B82F6', colors.info?.light || '#60A5FA'],
          icon: 'information-circle',
        };
    }
  };

  const { gradient, icon } = getToastColors();

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        {
          transform: [{ translateY }, { scale }],
          opacity,
          top: position === 'top' ? 60 + (index * 80) : undefined,
          bottom: position === 'bottom' ? 60 + (index * 80) : undefined,
        },
      ]}
    >
      <LinearGradient
        colors={gradient}
        style={styles.toastGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.toastContent}>
          <View style={styles.toastIcon}>
            <Ionicons name={icon as any} size={24} color="#FFFFFF" />
          </View>

          <View style={styles.toastText}>
            <Text style={styles.toastTitle} numberOfLines={1}>
              {toast.title}
            </Text>
            {toast.message && (
              <Text style={styles.toastMessage} numberOfLines={2}>
                {toast.message}
              </Text>
            )}
          </View>

          <View style={styles.toastActions}>
            {toast.action && (
              <EnhancedTouchTarget
                style={styles.actionButton}
                onPress={toast.action.onPress}
                accessibilityLabel={toast.action.label}
                minimumSize={32}
                showTouchFeedback={true}
              >
                <Text style={styles.actionButtonText}>{toast.action.label}</Text>
              </EnhancedTouchTarget>
            )}

            <EnhancedTouchTarget
              style={styles.dismissButton}
              onPress={handleDismiss}
              accessibilityLabel="Dismiss notification"
              minimumSize={32}
              showTouchFeedback={true}
            >
              <Ionicons name="close" size={20} color="#FFFFFF" />
            </EnhancedTouchTarget>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

const createStyles = (colors: any, position: 'top' | 'bottom') => StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
  toastContainer: {
    position: 'absolute',
    left: getResponsiveSpacing(4),
    right: getResponsiveSpacing(4),
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  toastGradient: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  toastContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: getResponsiveSpacing(4),
    gap: getResponsiveSpacing(3),
  },
  toastIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toastText: {
    flex: 1,
  },
  toastTitle: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  toastMessage: {
    fontSize: getResponsiveFontSize(14),
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 18,
  },
  toastActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing(2),
  },
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingHorizontal: getResponsiveSpacing(3),
    paddingVertical: getResponsiveSpacing(2),
  },
  actionButtonText: {
    fontSize: getResponsiveFontSize(12),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  dismissButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

// Export toast utilities
export const useToast = () => {
  const toastRef = useRef<{
    showToast: (config: Omit<ToastConfig, 'id'>) => void;
    dismissAllToasts: () => void;
  }>(null);

  const showSuccess = useCallback((title: string, message?: string, options?: Partial<ToastConfig>) => {
    toastRef.current?.showToast({
      type: 'success',
      title,
      message,
      ...options,
    });
  }, []);

  const showError = useCallback((title: string, message?: string, options?: Partial<ToastConfig>) => {
    toastRef.current?.showToast({
      type: 'error',
      title,
      message,
      ...options,
    });
  }, []);

  const showWarning = useCallback((title: string, message?: string, options?: Partial<ToastConfig>) => {
    toastRef.current?.showToast({
      type: 'warning',
      title,
      message,
      ...options,
    });
  }, []);

  const showInfo = useCallback((title: string, message?: string, options?: Partial<ToastConfig>) => {
    toastRef.current?.showToast({
      type: 'info',
      title,
      message,
      ...options,
    });
  }, []);

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    dismissAll: () => toastRef.current?.dismissAllToasts(),
  };
};
