/**
 * Enhanced User Feedback System
 * Provides comprehensive user feedback with contextual actions
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  Animated, 
  StyleSheet, 
  Dimensions,
  PanGestureHandler,
  State as GestureState,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';

const { width: screenWidth } = Dimensions.get('window');

export interface FeedbackMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'loading';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  actions?: FeedbackAction[];
  onDismiss?: () => void;
  progress?: number; // 0-100 for loading type
  undoable?: boolean;
  onUndo?: () => void;
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

export interface FeedbackAction {
  label: string;
  onPress: () => void;
  style?: 'primary' | 'secondary' | 'destructive';
  icon?: string;
}

interface FeedbackConfig {
  icon: string;
  color: string;
  backgroundColor: string;
  borderColor: string;
}

const getFeedbackConfig = (type: FeedbackMessage['type'], colors: any): FeedbackConfig => {
  switch (type) {
    case 'success':
      return {
        icon: 'checkmark-circle',
        color: '#4CAF50',
        backgroundColor: '#E8F5E8',
        borderColor: '#4CAF50',
      };
    case 'error':
      return {
        icon: 'close-circle',
        color: '#FF6B6B',
        backgroundColor: '#FFF5F5',
        borderColor: '#FF6B6B',
      };
    case 'warning':
      return {
        icon: 'warning',
        color: '#FFA726',
        backgroundColor: '#FFF8E1',
        borderColor: '#FFA726',
      };
    case 'info':
      return {
        icon: 'information-circle',
        color: '#42A5F5',
        backgroundColor: '#E3F2FD',
        borderColor: '#42A5F5',
      };
    case 'loading':
      return {
        icon: 'hourglass',
        color: colors.primary.default,
        backgroundColor: colors.background.secondary,
        borderColor: colors.primary.default,
      };
    default:
      return {
        icon: 'information-circle',
        color: colors.text.primary,
        backgroundColor: colors.background.secondary,
        borderColor: colors.border.primary,
      };
  }
};

interface FeedbackItemProps {
  message: FeedbackMessage;
  onDismiss: (id: string) => void;
  index: number;
}

const FeedbackItem: React.FC<FeedbackItemProps> = ({ message, onDismiss, index }) => {
  const { colors } = useTheme();
  const config = getFeedbackConfig(message.type, colors);
  
  const translateY = useRef(new Animated.Value(-100)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const progress = useRef(new Animated.Value(message.progress || 0)).current;
  
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-dismiss timer
    if (!message.persistent && message.duration && message.duration > 0) {
      timeoutRef.current = setTimeout(() => {
        handleDismiss();
      }, message.duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Update progress for loading messages
    if (message.type === 'loading' && message.progress !== undefined) {
      Animated.timing(progress, {
        toValue: message.progress,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [message.progress]);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: -100,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(message.id);
      message.onDismiss?.();
    });
  };

  const handleSwipeGesture = (event: any) => {
    const { translationX, state } = event.nativeEvent;
    
    if (state === GestureState.ACTIVE) {
      translateX.setValue(translationX);
    } else if (state === GestureState.END) {
      if (Math.abs(translationX) > screenWidth * 0.3) {
        // Swipe to dismiss
        Animated.timing(translateX, {
          toValue: translationX > 0 ? screenWidth : -screenWidth,
          duration: 200,
          useNativeDriver: true,
        }).start(() => handleDismiss());
      } else {
        // Snap back
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  const renderActions = () => {
    if (!message.actions && !message.undoable) return null;

    return (
      <View style={styles.actionsContainer}>
        {message.undoable && (
          <TouchableOpacity
            style={[styles.actionButton, styles.undoButton]}
            onPress={() => {
              message.onUndo?.();
              handleDismiss();
            }}
          >
            <Ionicons name="arrow-undo" size={16} color={config.color} />
            <Text style={[styles.actionText, { color: config.color }]}>Undo</Text>
          </TouchableOpacity>
        )}
        
        {message.actions?.map((action, actionIndex) => (
          <TouchableOpacity
            key={actionIndex}
            style={[
              styles.actionButton,
              action.style === 'primary' && { backgroundColor: config.color },
              action.style === 'destructive' && { backgroundColor: '#FF6B6B' },
            ]}
            onPress={() => {
              action.onPress();
              if (!message.persistent) {
                handleDismiss();
              }
            }}
          >
            {action.icon && (
              <Ionicons 
                name={action.icon as any} 
                size={16} 
                color={action.style === 'primary' ? '#FFFFFF' : config.color} 
              />
            )}
            <Text 
              style={[
                styles.actionText,
                { 
                  color: action.style === 'primary' || action.style === 'destructive' 
                    ? '#FFFFFF' 
                    : config.color 
                }
              ]}
            >
              {action.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderProgressBar = () => {
    if (message.type !== 'loading' || message.progress === undefined) return null;

    return (
      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: colors.background.tertiary }]}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                backgroundColor: config.color,
                width: progress.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: colors.text.secondary }]}>
          {Math.round(message.progress)}%
        </Text>
      </View>
    );
  };

  return (
    <PanGestureHandler onGestureEvent={handleSwipeGesture}>
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor: config.backgroundColor,
            borderLeftColor: config.borderColor,
            transform: [
              { translateY },
              { translateX },
            ],
            opacity,
            top: index * 80, // Stack messages
          },
        ]}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Ionicons name={config.icon as any} size={24} color={config.color} />
            
            <View style={styles.textContainer}>
              {message.title && (
                <Text style={[styles.title, { color: config.color }]}>
                  {message.title}
                </Text>
              )}
              <Text style={[styles.message, { color: colors.text.primary }]}>
                {message.message}
              </Text>
            </View>

            {!message.persistent && (
              <TouchableOpacity
                style={styles.dismissButton}
                onPress={handleDismiss}
              >
                <Ionicons name="close" size={20} color={colors.text.secondary} />
              </TouchableOpacity>
            )}
          </View>

          {renderProgressBar()}
          {renderActions()}
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
};

interface EnhancedUserFeedbackProps {
  messages: FeedbackMessage[];
  onDismiss: (id: string) => void;
  maxVisible?: number;
  position?: 'top' | 'bottom';
}

export const EnhancedUserFeedback: React.FC<EnhancedUserFeedbackProps> = ({
  messages,
  onDismiss,
  maxVisible = 3,
  position = 'top',
}) => {
  // Sort messages by priority and timestamp
  const sortedMessages = messages
    .sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority || 'medium'];
      const bPriority = priorityOrder[b.priority || 'medium'];
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      return parseInt(b.id) - parseInt(a.id); // Newer first
    })
    .slice(0, maxVisible);

  if (sortedMessages.length === 0) return null;

  return (
    <View style={[styles.feedbackContainer, position === 'bottom' && styles.bottomPosition]}>
      {sortedMessages.map((message, index) => (
        <FeedbackItem
          key={message.id}
          message={message}
          onDismiss={onDismiss}
          index={index}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  feedbackContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 1000,
    pointerEvents: 'box-none',
  },
  bottomPosition: {
    top: undefined,
    bottom: 50,
  },
  container: {
    marginHorizontal: getResponsiveSpacing(3),
    marginVertical: getResponsiveSpacing(1),
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  content: {
    padding: getResponsiveSpacing(3),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  textContainer: {
    flex: 1,
    marginLeft: getResponsiveSpacing(2),
  },
  title: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '600',
    marginBottom: 2,
  },
  message: {
    fontSize: getResponsiveFontSize(14),
    lineHeight: 20,
  },
  dismissButton: {
    padding: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: getResponsiveSpacing(2),
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    marginLeft: 8,
    minWidth: 35,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginTop: getResponsiveSpacing(2),
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'transparent',
    gap: 4,
  },
  undoButton: {
    borderWidth: 1,
    borderColor: 'currentColor',
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
