/**
 * Enhanced Loading States Component
 * Provides comprehensive loading indicators with progress tracking
 */

import React, { useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  ActivityIndicator, 
  StyleSheet, 
  Animated, 
  ViewStyle,
  TextStyle 
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';

export interface LoadingStateProps {
  variant?: 'spinner' | 'dots' | 'skeleton' | 'progress' | 'pulse';
  size?: 'small' | 'medium' | 'large';
  message?: string;
  progress?: number; // 0-100
  showProgress?: boolean;
  overlay?: boolean;
  style?: ViewStyle;
  messageStyle?: TextStyle;
  color?: string;
  testID?: string;
  accessibilityLabel?: string;
  duration?: number; // Animation duration in ms
  steps?: string[]; // For stepped loading
  currentStep?: number;
}

export const EnhancedLoadingState: React.FC<LoadingStateProps> = ({
  variant = 'spinner',
  size = 'medium',
  message = 'Loading...',
  progress = 0,
  showProgress = false,
  overlay = false,
  style,
  messageStyle,
  color,
  testID = 'loading-state',
  accessibilityLabel,
  duration = 1000,
  steps = [],
  currentStep = 0,
}) => {
  const { colors } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;
  const dotsValues = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  const loadingColor = color || colors.primary.default;

  useEffect(() => {
    if (variant === 'pulse') {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 0.6,
            duration: duration / 2,
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 1,
            duration: duration / 2,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      return () => pulseAnimation.stop();
    }

    if (variant === 'dots') {
      const dotsAnimation = Animated.loop(
        Animated.stagger(200, 
          dotsValues.map(value =>
            Animated.sequence([
              Animated.timing(value, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
              }),
              Animated.timing(value, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
              }),
            ])
          )
        )
      );
      dotsAnimation.start();
      return () => dotsAnimation.stop();
    }
  }, [variant, duration, pulseValue, dotsValues]);

  const getSizeValue = () => {
    switch (size) {
      case 'small':
        return 'small' as const;
      case 'large':
        return 'large' as const;
      default:
        return 'small' as const;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 40;
      default:
        return 28;
    }
  };

  const renderSpinner = () => (
    <ActivityIndicator
      size={getSizeValue()}
      color={loadingColor}
      accessibilityLabel={accessibilityLabel || 'Loading'}
    />
  );

  const renderDots = () => (
    <View style={styles.dotsContainer}>
      {dotsValues.map((value, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              backgroundColor: loadingColor,
              opacity: value,
              transform: [{ scale: value }],
            },
          ]}
        />
      ))}
    </View>
  );

  const renderSkeleton = () => (
    <View style={styles.skeletonContainer}>
      <Animated.View
        style={[
          styles.skeletonLine,
          styles.skeletonTitle,
          { backgroundColor: colors.background.secondary, opacity: pulseValue },
        ]}
      />
      <Animated.View
        style={[
          styles.skeletonLine,
          styles.skeletonSubtitle,
          { backgroundColor: colors.background.secondary, opacity: pulseValue },
        ]}
      />
      <Animated.View
        style={[
          styles.skeletonLine,
          styles.skeletonContent,
          { backgroundColor: colors.background.secondary, opacity: pulseValue },
        ]}
      />
    </View>
  );

  const renderProgress = () => (
    <View style={styles.progressContainer}>
      <View style={[styles.progressBar, { backgroundColor: colors.background.secondary }]}>
        <View
          style={[
            styles.progressFill,
            {
              backgroundColor: loadingColor,
              width: `${Math.min(Math.max(progress, 0), 100)}%`,
            },
          ]}
        />
      </View>
      {showProgress && (
        <Text style={[styles.progressText, { color: colors.text.secondary }]}>
          {Math.round(progress)}%
        </Text>
      )}
    </View>
  );

  const renderPulse = () => (
    <Animated.View
      style={[
        styles.pulseContainer,
        {
          backgroundColor: loadingColor,
          opacity: pulseValue,
          transform: [{ scale: pulseValue }],
        },
      ]}
    />
  );

  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'skeleton':
        return renderSkeleton();
      case 'progress':
        return renderProgress();
      case 'pulse':
        return renderPulse();
      case 'spinner':
      default:
        return renderSpinner();
    }
  };

  const renderSteps = () => {
    if (steps.length === 0) return null;

    return (
      <View style={styles.stepsContainer}>
        <Text style={[styles.stepText, { color: colors.text.secondary }]}>
          Step {currentStep + 1} of {steps.length}
        </Text>
        <Text style={[styles.stepLabel, { color: colors.text.primary }]}>
          {steps[currentStep] || 'Processing...'}
        </Text>
        <View style={styles.stepIndicator}>
          {steps.map((_, index) => (
            <View
              key={index}
              style={[
                styles.stepDot,
                {
                  backgroundColor: index <= currentStep 
                    ? loadingColor 
                    : colors.background.secondary,
                },
              ]}
            />
          ))}
        </View>
      </View>
    );
  };

  const containerStyle: ViewStyle = [
    styles.container,
    overlay && styles.overlay,
    style,
  ];

  const contentStyle: ViewStyle = [
    styles.content,
    variant === 'skeleton' && styles.skeletonContent,
  ];

  return (
    <View 
      style={containerStyle} 
      testID={testID}
      accessibilityRole="progressbar"
      accessibilityLabel={accessibilityLabel || message}
      accessibilityState={{ busy: true }}
    >
      <View style={contentStyle}>
        {renderLoadingIndicator()}
        
        {message && variant !== 'skeleton' && (
          <Text 
            style={[
              styles.message, 
              { color: colors.text.secondary },
              messageStyle,
            ]}
          >
            {message}
          </Text>
        )}

        {renderSteps()}
      </View>
    </View>
  );
};

// Convenience components
export const LoadingSpinner: React.FC<Omit<LoadingStateProps, 'variant'>> = (props) => (
  <EnhancedLoadingState {...props} variant="spinner" />
);

export const LoadingDots: React.FC<Omit<LoadingStateProps, 'variant'>> = (props) => (
  <EnhancedLoadingState {...props} variant="dots" />
);

export const LoadingSkeleton: React.FC<Omit<LoadingStateProps, 'variant'>> = (props) => (
  <EnhancedLoadingState {...props} variant="skeleton" />
);

export const LoadingProgress: React.FC<Omit<LoadingStateProps, 'variant'>> = (props) => (
  <EnhancedLoadingState {...props} variant="progress" />
);

export const LoadingPulse: React.FC<Omit<LoadingStateProps, 'variant'>> = (props) => (
  <EnhancedLoadingState {...props} variant="pulse" />
);

export const LoadingOverlay: React.FC<LoadingStateProps> = (props) => (
  <EnhancedLoadingState {...props} overlay />
);

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing(4),
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 1000,
  },
  content: {
    alignItems: 'center',
  },
  skeletonContent: {
    width: '100%',
    maxWidth: 300,
  },
  message: {
    fontSize: getResponsiveFontSize(14),
    marginTop: getResponsiveSpacing(2),
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  skeletonContainer: {
    width: '100%',
  },
  skeletonLine: {
    height: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  skeletonTitle: {
    width: '70%',
    height: 16,
  },
  skeletonSubtitle: {
    width: '50%',
    height: 14,
  },
  skeletonContent: {
    width: '90%',
    height: 12,
  },
  progressContainer: {
    width: '100%',
    maxWidth: 200,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    marginTop: 4,
  },
  pulseContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  stepsContainer: {
    marginTop: getResponsiveSpacing(3),
    alignItems: 'center',
  },
  stepText: {
    fontSize: 12,
    marginBottom: 4,
  },
  stepLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepIndicator: {
    flexDirection: 'row',
    gap: 6,
  },
  stepDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
});
