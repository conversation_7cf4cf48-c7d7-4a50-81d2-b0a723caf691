/**
 * Toast Notification System
 * Provides user feedback through toast notifications
 */

import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from 'react-native';
import { Dimensions } from 'react-native';
import { StyleSheet, Animated, PanGestureHandler, PanGestureHandlerGestureEvent, State,  } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getResponsiveSpacing, getResponsiveFontSize } from '../../utils/responsiveUtils';
import { Colors } from '../../constants/Colors';

export interface ToastProps {
  id: string;
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  position?: 'top' | 'bottom';
  action?: {
    label: string;
    onPress: () => void;
  };
  onDismiss?: () => void;
  swipeable?: boolean;
}

export const Toast: React.FC<ToastProps> = ({
  id,
  message,
  type = 'info',
  duration = 4000,
  position = 'top',
  action,
  onDismiss,
  swipeable = true,
}) => {
  const [visible, setVisible] = useState(false);
  const translateY = useRef(new Animated.Value(position === 'top' ? -100 : 100)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  const config = getToastConfig(type);

  useEffect(() => {
    // Show animation
    setVisible(true);
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss
    if (duration > 0) {
      const timer = setTimeout(() => {
        dismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, []);

  const dismiss = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -100 : 100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setVisible(false);
      onDismiss?.();
    });
  };

  const onGestureEvent = (event: PanGestureHandlerGestureEvent) => {
    if (!swipeable) return;
    translateX.setValue(event.nativeEvent.translationX);
  };

  const onHandlerStateChange = (event: PanGestureHandlerGestureEvent) => {
    if (!swipeable) return;
    
    if (event.nativeEvent.state === State.END) {
      const { translationX, velocityX } = event.nativeEvent;
      const shouldDismiss = Math.abs(translationX) > 100 || Math.abs(velocityX) > 500;

      if (shouldDismiss) {
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: translationX > 0 ? 400 : -400,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start(() => {
          setVisible(false);
          onDismiss?.();
        });
      } else {
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  if (!visible) return null;

  const containerStyle = [
    styles.container,
    styles[position],
    {
      backgroundColor: config.backgroundColor,
      borderLeftColor: config.borderColor,
    },
  ];

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
      enabled={swipeable}
    >
      <Animated.View
        style={[
          containerStyle,
          {
            transform: [{ translateY }, { translateX }],
            opacity,
          },
        ]}
      >
        <View style={styles.content}>
          <Ionicons
            name={config.icon}
            size={20}
            color={config.iconColor}
            style={styles.icon}
          />
          <Text style={[styles.message, { color: config.textColor }]} numberOfLines={3}>
            {message}
          </Text>
          {action && (
            <Text
              style={[styles.action, { color: config.actionColor }]}
              onPress={action.onPress}
            >
              {action.label}
            </Text>
          )}
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
};

// Toast Manager Context
interface ToastContextType {
  showToast: (toast: Omit<ToastProps, 'id' | 'onDismiss'>) => void;
  hideToast: (id: string) => void;
  hideAllToasts: () => void;
  showSuccess: (message: string, options?: Partial<ToastProps>) => void;
  showError: (message: string, options?: Partial<ToastProps>) => void;
  showWarning: (message: string, options?: Partial<ToastProps>) => void;
  showInfo: (message: string, options?: Partial<ToastProps>) => void;
}

const ToastContext = React.createContext<ToastContextType | null>(null);

export const useToast = () => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const showToast = (toast: Omit<ToastProps, 'id' | 'onDismiss'>) => {
    const id = Date.now().toString();
    const newToast: ToastProps = {
      ...toast,
      id,
      onDismiss: () => hideToast(id),
    };

    setToasts(prev => {
      // Limit to maximum 3 toasts
      const updated = [...prev, newToast];
      return updated.slice(-3);
    });
  };

  const hideToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const hideAllToasts = () => {
    setToasts([]);
  };

  // Enhanced toast methods
  const showSuccess = (message: string, options?: Partial<ToastProps>) => {
    showToast({
      message,
      type: 'success',
      duration: 3000,
      ...options,
    });
  };

  const showError = (message: string, options?: Partial<ToastProps>) => {
    showToast({
      message,
      type: 'error',
      duration: 5000,
      ...options,
    });
  };

  const showWarning = (message: string, options?: Partial<ToastProps>) => {
    showToast({
      message,
      type: 'warning',
      duration: 4000,
      ...options,
    });
  };

  const showInfo = (message: string, options?: Partial<ToastProps>) => {
    showToast({
      message,
      type: 'info',
      duration: 3000,
      ...options,
    });
  };

  const contextValue: ToastContextType = {
    showToast,
    hideToast,
    hideAllToasts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <View style={styles.toastContainer} pointerEvents="box-none">
        {toasts.map(toast => (
          <Toast key={toast.id} {...toast} />
        ))}
      </View>
    </ToastContext.Provider>
  );
};

// Helper function to get toast configuration
const getToastConfig = (type: 'success' | 'error' | 'warning' | 'info') => {
  switch (type) {
    case 'success':
      return {
        icon: 'checkmark-circle' as const,
        backgroundColor: Colors.success + '15',
        borderColor: Colors.success,
        iconColor: Colors.success,
        textColor: Colors.text.primary,
        actionColor: Colors.success,
      };
    case 'error':
      return {
        icon: 'alert-circle' as const,
        backgroundColor: Colors.error + '15',
        borderColor: Colors.error,
        iconColor: Colors.error,
        textColor: Colors.text.primary,
        actionColor: Colors.error,
      };
    case 'warning':
      return {
        icon: 'warning' as const,
        backgroundColor: Colors.warning + '15',
        borderColor: Colors.warning,
        iconColor: Colors.warning,
        textColor: Colors.text.primary,
        actionColor: Colors.warning,
      };
    case 'info':
      return {
        icon: 'information-circle' as const,
        backgroundColor: Colors.info + '15',
        borderColor: Colors.info,
        iconColor: Colors.info,
        textColor: Colors.text.primary,
        actionColor: Colors.info,
      };
  }
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
  container: {
    position: 'absolute',
    left: getResponsiveSpacing(16),
    right: getResponsiveSpacing(16),
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  top: {
    top: getResponsiveSpacing(60),
  },
  bottom: {
    bottom: getResponsiveSpacing(100),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: getResponsiveSpacing(16),
  },
  icon: {
    marginRight: getResponsiveSpacing(12),
  },
  message: {
    flex: 1,
    fontSize: getResponsiveFontSize(14),
    lineHeight: getResponsiveFontSize(20),
    fontWeight: '500',
  },
  action: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: '600',
    marginLeft: getResponsiveSpacing(12),
  },
});
