/**
 * Booking Details Screen - Comprehensive booking details and management
 *
 * Screen Contract:
 * - Display complete booking information with persistent summary
 * - Provide modification options (cancel, reschedule, contact provider)
 * - Implement undo functionality for critical actions
 * - Show real-time status updates and tracking
 * - Support accessibility and minimalist design principles
 * - Integrate with backend booking management APIs
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
  Share,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useBookingStore, Booking, BookingStatus } from '../../store/bookingSlice';
import { useAuthStore } from '../../store/authSlice';
import { useTheme } from '../../contexts/ThemeContext';
import { Colors } from '../../constants/Colors';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { useBookingFeedback } from '../../hooks/useActionFeedbackHooks';
import { useActionFeedback } from '../../components/ui/ActionFeedbackSystem';
import { Button } from '../../components/atoms/Button';
import { UndoToast } from '../../components/ui/UndoToast';

interface RouteParams {
  bookingId: string;
}

export const BookingDetailsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { bookingId } = route.params as RouteParams;
  const { colors } = useTheme();
  
  // Store hooks
  const { user } = useAuthStore();
  const {
    currentBooking,
    isLoading,
    error,
    loadBookingDetails,
    cancelBooking,
    clearError,
  } = useBookingStore();

  // Action feedback hooks
  const { 
    cancelBooking: cancelBookingWithFeedback, 
    rescheduleBooking,
    contactProvider 
  } = useBookingFeedback();
  const { showConfirmation } = useActionFeedback();

  // Local state
  const [refreshing, setRefreshing] = useState(false);
  const [showUndoToast, setShowUndoToast] = useState(false);
  const [undoOperationId, setUndoOperationId] = useState<string | null>(null);

  // Load booking details on mount and focus
  useFocusEffect(
    useCallback(() => {
      if (bookingId) {
        loadBookingDetails(bookingId);
      }
    }, [bookingId, loadBookingDetails])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadBookingDetails(bookingId);
    } catch (error) {
      console.error('Failed to refresh booking details:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCancelBooking = async () => {
    if (!currentBooking) return;

    try {
      // Show confirmation dialog
      const confirmed = await showConfirmation({
        title: 'Cancel Booking',
        message: 'Are you sure you want to cancel this booking? This action can be undone within 5 minutes.',
        confirmText: 'Cancel Booking',
        cancelText: 'Keep Booking',
        destructive: true,
      });

      if (confirmed) {
        // Cancel booking with feedback
        await cancelBookingWithFeedback(currentBooking.id);

        // Create undo operation ID
        const operationId = `cancel-booking-${currentBooking.id}-${Date.now()}`;
        setUndoOperationId(operationId);

        // Show undo toast
        setShowUndoToast(true);

        // Auto-hide undo toast after 5 minutes
        setTimeout(() => {
          setShowUndoToast(false);
          setUndoOperationId(null);
        }, 300000); // 5 minutes
      }
    } catch (error) {
      console.error('Failed to cancel booking:', error);
    }
  };

  const handleUndoCancel = async () => {
    if (!undoOperationId) return;

    try {
      // Restore booking (this would call a restore API endpoint)
      // For now, we'll just reload the booking details
      await loadBookingDetails(bookingId);

      setShowUndoToast(false);
      setUndoOperationId(null);
    } catch (error) {
      console.error('Failed to undo cancellation:', error);
    }
  };

  const handleRescheduleBooking = () => {
    if (!currentBooking) return;
    
    navigation.navigate('BookingReschedule' as never, { 
      bookingId: currentBooking.id 
    } as never);
  };

  const handleContactProvider = async () => {
    if (!currentBooking?.provider) return;

    try {
      await contactProvider(currentBooking.provider.id, currentBooking.id);
      
      // Navigate to messages screen
      navigation.navigate('Messages' as never, {
        providerId: currentBooking.provider.id,
        bookingId: currentBooking.id,
      } as never);
    } catch (error) {
      console.error('Failed to contact provider:', error);
    }
  };

  const handleCallProvider = () => {
    if (!currentBooking?.provider?.phone) return;
    
    const phoneUrl = `tel:${currentBooking.provider.phone}`;
    Linking.openURL(phoneUrl);
  };

  const handleShareBooking = async () => {
    if (!currentBooking) return;

    try {
      const shareContent = {
        message: `Booking Details:\n\nService: ${currentBooking.service.name}\nProvider: ${currentBooking.provider.businessName}\nDate: ${formatDate(currentBooking.scheduledDateTime)}\nTime: ${formatTime(currentBooking.scheduledDateTime)}\nReference: ${currentBooking.referenceNumber}`,
        title: 'Booking Details',
      };

      await Share.share(shareContent);
    } catch (error) {
      console.error('Failed to share booking:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'confirmed':
        return colors.success || Colors.light.success;
      case 'pending':
        return colors.warning || Colors.light.warning;
      case 'cancelled':
        return colors.error || Colors.light.error;
      case 'completed':
        return colors.info || Colors.light.info;
      default:
        return colors.textSecondary || Colors.light.textSecondary;
    }
  };

  const getStatusText = (status: BookingStatus) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending Confirmation';
      case 'cancelled':
        return 'Cancelled';
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      default:
        return status;
    }
  };

  const canCancelBooking = () => {
    if (!currentBooking) return false;
    
    const cancellableStatuses = ['pending', 'confirmed'];
    return cancellableStatuses.includes(currentBooking.status);
  };

  const canRescheduleBooking = () => {
    if (!currentBooking) return false;
    
    const reschedulableStatuses = ['pending', 'confirmed'];
    return reschedulableStatuses.includes(currentBooking.status);
  };

  // Loading state
  if (isLoading && !currentBooking) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={styles.loadingText}>Loading booking details...</Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error && !currentBooking) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.emptyStateText}>Failed to load booking details</Text>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => loadBookingDetails(bookingId)}
          >
            <Text style={styles.actionButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // No booking found
  if (!currentBooking) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.emptyStateText}>Booking not found</Text>
          <Button
            onPress={() => navigation.goBack()}
            style={{ marginTop: 16 }}
          >
            Go Back
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary || Colors.light.primary]}
          />
        }
      >
        {/* Persistent Summary Header */}
        <View style={styles.summaryHeader}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(currentBooking.status) + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor(currentBooking.status) }]}>
              {getStatusText(currentBooking.status)}
            </Text>
          </View>
          <Text style={styles.referenceNumber}>#{currentBooking.referenceNumber}</Text>
        </View>

        {/* Service Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Service Details</Text>
          <View style={styles.serviceCard}>
            <Text style={styles.serviceName}>{currentBooking.service.name}</Text>
            <Text style={styles.servicePrice}>${currentBooking.service.price}</Text>
            <Text style={styles.serviceDuration}>{currentBooking.service.duration} minutes</Text>
          </View>
        </View>

        {/* Provider Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Provider</Text>
          <View style={styles.providerCard}>
            <View style={styles.providerInfo}>
              <Text style={styles.providerName}>{currentBooking.provider.businessName}</Text>
              <Text style={styles.providerAddress}>{currentBooking.provider.address}</Text>
              {currentBooking.provider.phone && (
                <TouchableOpacity style={styles.providerPhone} onPress={handleCallProvider}>
                  <Ionicons name="call" size={16} color={colors.primary} />
                  <Text style={styles.providerPhoneText}>{currentBooking.provider.phone}</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>

        {/* Appointment Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appointment</Text>
          <View style={styles.appointmentCard}>
            <Text style={styles.appointmentDate}>{formatDate(currentBooking.scheduledDateTime)}</Text>
            <Text style={styles.appointmentTime}>{formatTime(currentBooking.scheduledDateTime)}</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsSection}>
          <TouchableOpacity style={styles.actionButton} onPress={handleContactProvider}>
            <Ionicons name="chatbubble-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Message Provider</Text>
          </TouchableOpacity>

          {canRescheduleBooking() && (
            <TouchableOpacity style={styles.actionButton} onPress={handleRescheduleBooking}>
              <Ionicons name="calendar-outline" size={20} color={colors.primary} />
              <Text style={styles.actionButtonText}>Reschedule</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.actionButton} onPress={handleShareBooking}>
            <Ionicons name="share-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Share</Text>
          </TouchableOpacity>

          {canCancelBooking() && (
            <TouchableOpacity style={styles.cancelButton} onPress={handleCancelBooking}>
              <Ionicons name="close-circle-outline" size={20} color={colors.error} />
              <Text style={styles.cancelButtonText}>Cancel Booking</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>

      {/* Undo Toast */}
      {showUndoToast && undoOperationId && (
        <UndoToast
          operationId={undoOperationId}
          visible={showUndoToast}
          onUndo={handleUndoCancel}
          onDismiss={() => setShowUndoToast(false)}
        />
      )}
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 18,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  statusBadge: {
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  statusText: {
    fontWeight: '600',
    fontSize: 14,
  },
  referenceNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textPrimary,
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.textPrimary,
    marginBottom: 12,
  },
  serviceCard: {
    backgroundColor: Colors.light.surface,
    padding: 16,
    borderRadius: 12,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textPrimary,
    marginBottom: 4,
  },
  servicePrice: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  serviceDuration: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  providerCard: {
    backgroundColor: Colors.light.surface,
    padding: 16,
    borderRadius: 12,
  },
  providerInfo: {},
  providerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textPrimary,
    marginBottom: 4,
  },
  providerAddress: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  providerPhone: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  providerPhoneText: {
    marginLeft: 8,
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '500',
  },
  appointmentCard: {
    backgroundColor: Colors.light.surface,
    padding: 16,
    borderRadius: 12,
  },
  appointmentDate: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textPrimary,
    marginBottom: 4,
  },
  appointmentTime: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  actionsSection: {
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.surface,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  actionButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.primary,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.error + '20',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.error,
  },
  cancelButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.error,
  },
});

export default BookingDetailsScreen;
