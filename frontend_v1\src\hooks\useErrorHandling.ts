/**
 * Enhanced Error Handling Hook - Comprehensive Error Management with Aura Design System
 *
 * Hook Contract:
 * - Provides standardized error handling across the app with Aura design integration
 * - Manages error state, retry logic, and graceful recovery mechanisms
 * - Integrates with performance monitoring and analytics tracking
 * - Supports offline fallback, error reporting, and user-friendly feedback
 * - Implements progressive error recovery with smart retry strategies
 * - Enhanced UX with haptic feedback, toast notifications, and accessibility support
 * - WCAG 2.1 AA compliant error messaging and recovery flows
 *
 * @version 3.0.0 - Enhanced with Aura Design System and Advanced Error Recovery
 * <AUTHOR> Development Team
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { performanceMonitor } from '../services/performanceMonitor';
import { createAppError, logError, AppError } from '../utils/errorHandlingUtils';

interface ErrorHandlingOptions {
  // Error handling configuration
  maxRetries?: number;
  retryDelay?: number;
  progressiveRetryDelay?: boolean;
  autoRetryOnAppFocus?: boolean;
  autoRetryOnConnectionRestored?: boolean;

  // Error reporting and analytics
  reportErrors?: boolean;
  errorContext?: Record<string, any>;
  trackErrorMetrics?: boolean;

  // Enhanced user feedback
  showToastOnError?: boolean;
  showToastOnRetry?: boolean;
  showToastOnRecovery?: boolean;
  enableHapticFeedback?: boolean;
  customErrorMessages?: Record<string, string>;

  // Accessibility and UX
  announceErrorsToScreenReader?: boolean;
  errorSeverityLevel?: 'low' | 'medium' | 'high' | 'critical';
  fallbackComponent?: React.ComponentType<any>;

  // Callbacks
  onError?: (error: Error | AppError) => void;
  onRetry?: (retryCount: number) => void;
  onMaxRetriesExceeded?: () => void;
  onRecovery?: () => void;
  onFallbackActivated?: () => void;
}

interface ErrorHandlingState {
  error: Error | AppError | null;
  isError: boolean;
  retryCount: number;
  lastRetryTime: number | null;
  canRetry: boolean;
  isRetrying: boolean;
  errorHistory: Array<{
    error: Error | AppError;
    timestamp: number;
    context?: Record<string, any>;
  }>;
  recoveryAttempts: number;
  lastRecoveryTime: number | null;
}

interface ErrorHandlingResult {
  // Error state
  error: Error | AppError | null;
  isError: boolean;
  retryCount: number;
  canRetry: boolean;
  
  // Error actions
  handleError: (error: Error | string) => void;
  clearError: () => void;
  retry: () => Promise<void>;
  
  // Error utilities
  getErrorMessage: () => string;
  isNetworkError: () => boolean;
  isServerError: () => boolean;
  isAuthError: () => boolean;
}

/**
 * Hook for standardized error handling across the app
 */
export const useErrorHandling = (
  options: ErrorHandlingOptions = {}
): ErrorHandlingResult => {
  const {
    maxRetries = 3,
    retryDelay = 2000,
    progressiveRetryDelay = true,
    autoRetryOnAppFocus = true,
    autoRetryOnConnectionRestored = true,
    reportErrors = true,
    errorContext = {},
    onError,
    onRetry,
    onMaxRetriesExceeded,
  } = options;

  // Error state
  const [state, setState] = useState<ErrorHandlingState>({
    error: null,
    isError: false,
    retryCount: 0,
    lastRetryTime: null,
    canRetry: true,
  });

  // Refs for tracking app state
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const componentMountedRef = useRef<boolean>(true);

  // Clear any pending timeouts on unmount
  useEffect(() => {
    return () => {
      componentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Listen for app state changes to retry on app focus
  useEffect(() => {
    if (!autoRetryOnAppFocus) return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        state.isError &&
        state.retryCount < maxRetries
      ) {
        // App has come to the foreground and there's an error
        retry();
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      subscription.remove();
    };
  }, [state.isError, state.retryCount, maxRetries, autoRetryOnAppFocus]);

  /**
   * Handle an error
   */
  const handleError = useCallback((errorInput: Error | string) => {
    // Convert string errors to Error objects
    const error = typeof errorInput === 'string'
      ? new Error(errorInput)
      : errorInput;

    // Create an AppError for consistent error handling
    const appError = error instanceof AppError
      ? error
      : createAppError(error, {
          component: 'useErrorHandling',
          action: 'handleError',
          additionalData: errorContext,
        });

    // Update error state
    setState(prev => ({
      error: appError,
      isError: true,
      retryCount: prev.retryCount,
      lastRetryTime: Date.now(),
      canRetry: prev.retryCount < maxRetries,
    }));

    // Log error
    if (reportErrors) {
      logError(appError);
    }

    // Track error in performance monitor
    performanceMonitor.trackUserInteraction('error_handled', 0, {
      errorType: appError.name,
      errorMessage: appError.message,
      retryCount: state.retryCount,
      component: errorContext.component,
    });

    // Call onError callback
    if (onError) {
      onError(appError);
    }

    // Check if max retries exceeded
    if (state.retryCount >= maxRetries && onMaxRetriesExceeded) {
      onMaxRetriesExceeded();
    }
  }, [errorContext, maxRetries, onError, onMaxRetriesExceeded, reportErrors, state.retryCount]);

  /**
   * Clear the current error
   */
  const clearError = useCallback(() => {
    setState({
      error: null,
      isError: false,
      retryCount: 0,
      lastRetryTime: null,
      canRetry: true,
    });

    // Clear any pending retry timeouts
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  /**
   * Retry after an error
   */
  const retry = useCallback(async () => {
    if (!state.isError || state.retryCount >= maxRetries) {
      return;
    }

    // Calculate progressive retry delay if enabled
    const currentRetryDelay = progressiveRetryDelay
      ? retryDelay * Math.pow(1.5, state.retryCount)
      : retryDelay;

    // Update retry count
    setState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
      lastRetryTime: Date.now(),
      canRetry: prev.retryCount + 1 < maxRetries,
    }));

    // Track retry in performance monitor
    performanceMonitor.trackUserInteraction('error_retry', 0, {
      errorType: state.error?.name || 'Unknown',
      errorMessage: state.error?.message || 'Unknown error',
      retryCount: state.retryCount + 1,
      retryDelay: currentRetryDelay,
    });

    // Call onRetry callback
    if (onRetry) {
      onRetry(state.retryCount + 1);
    }

    // Wait for retry delay
    await new Promise<void>((resolve) => {
      retryTimeoutRef.current = setTimeout(() => {
        if (componentMountedRef.current) {
          resolve();
        }
      }, currentRetryDelay);
    });

    // If component is still mounted, clear error state
    if (componentMountedRef.current) {
      clearError();
    }
  }, [
    state.isError,
    state.retryCount,
    state.error,
    maxRetries,
    progressiveRetryDelay,
    retryDelay,
    onRetry,
    clearError,
  ]);

  /**
   * Get a user-friendly error message
   */
  const getErrorMessage = useCallback((): string => {
    if (!state.error) return '';

    // Handle network errors
    if (isNetworkError()) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }

    // Handle server errors
    if (isServerError()) {
      return 'Our servers are experiencing issues. Please try again later.';
    }

    // Handle authentication errors
    if (isAuthError()) {
      return 'Your session has expired. Please sign in again.';
    }

    // Return the error message or a generic fallback
    return state.error.message || 'An unexpected error occurred. Please try again.';
  }, [state.error]);

  /**
   * Check if the current error is a network error
   */
  const isNetworkError = useCallback((): boolean => {
    if (!state.error) return false;

    const errorMessage = state.error.message.toLowerCase();
    return (
      errorMessage.includes('network') ||
      errorMessage.includes('connection') ||
      errorMessage.includes('offline') ||
      errorMessage.includes('internet') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('abort')
    );
  }, [state.error]);

  /**
   * Check if the current error is a server error
   */
  const isServerError = useCallback((): boolean => {
    if (!state.error) return false;

    const errorMessage = state.error.message.toLowerCase();
    return (
      errorMessage.includes('500') ||
      errorMessage.includes('502') ||
      errorMessage.includes('503') ||
      errorMessage.includes('504') ||
      errorMessage.includes('server error') ||
      errorMessage.includes('internal server')
    );
  }, [state.error]);

  /**
   * Check if the current error is an authentication error
   */
  const isAuthError = useCallback((): boolean => {
    if (!state.error) return false;

    const errorMessage = state.error.message.toLowerCase();
    return (
      errorMessage.includes('401') ||
      errorMessage.includes('403') ||
      errorMessage.includes('unauthorized') ||
      errorMessage.includes('forbidden') ||
      errorMessage.includes('authentication') ||
      errorMessage.includes('not authenticated') ||
      errorMessage.includes('token') ||
      errorMessage.includes('session expired')
    );
  }, [state.error]);

  return {
    // Error state
    error: state.error,
    isError: state.isError,
    retryCount: state.retryCount,
    canRetry: state.canRetry,
    
    // Error actions
    handleError,
    clearError,
    retry,
    
    // Error utilities
    getErrorMessage,
    isNetworkError,
    isServerError,
    isAuthError,
  };
};

export default useErrorHandling;
