/**
 * Error Recovery Hook
 * Provides comprehensive error recovery mechanisms
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { AppState, AppStateStatus, NetInfo } from 'react-native';
import { useToast } from './useToast';
import { performanceMonitor } from '../services/performanceMonitor';
import { errorHandler } from '../utils/errorHandler';

export interface ErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  autoRetryOnNetworkRestore?: boolean;
  autoRetryOnAppFocus?: boolean;
  enableUserNotification?: boolean;
  enableAnalytics?: boolean;
  context?: Record<string, any>;
}

export interface ErrorRecoveryState {
  error: Error | null;
  isError: boolean;
  isRecovering: boolean;
  retryCount: number;
  lastRetryTime: number | null;
  canRetry: boolean;
  recoveryStrategy: string | null;
}

export interface ErrorRecoveryActions {
  handleError: (error: Error, recoveryStrategy?: string) => void;
  retry: () => Promise<void>;
  clearError: () => void;
  forceRetry: () => Promise<void>;
  setRecoveryStrategy: (strategy: string) => void;
}

export interface UseErrorRecoveryResult extends ErrorRecoveryState, ErrorRecoveryActions {
  getErrorMessage: () => string;
  isNetworkError: () => boolean;
  isServerError: () => boolean;
  isAuthError: () => boolean;
  getRecoveryRecommendation: () => string;
}

export const useErrorRecovery = (
  asyncOperation: () => Promise<any>,
  options: ErrorRecoveryOptions = {}
): UseErrorRecoveryResult => {
  const {
    maxRetries = 3,
    retryDelay = 2000,
    exponentialBackoff = true,
    autoRetryOnNetworkRestore = true,
    autoRetryOnAppFocus = true,
    enableUserNotification = true,
    enableAnalytics = true,
    context = {},
  } = options;

  const { showError, showSuccess } = useToast();
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const networkListenerRef = useRef<any>();
  const appStateRef = useRef(AppState.currentState);

  const [state, setState] = useState<ErrorRecoveryState>({
    error: null,
    isError: false,
    isRecovering: false,
    retryCount: 0,
    lastRetryTime: null,
    canRetry: true,
    recoveryStrategy: null,
  });

  // Calculate retry delay with exponential backoff
  const getRetryDelay = useCallback((retryCount: number): number => {
    if (!exponentialBackoff) return retryDelay;
    return Math.min(retryDelay * Math.pow(2, retryCount), 30000); // Max 30 seconds
  }, [retryDelay, exponentialBackoff]);

  // Handle error with recovery strategy
  const handleError = useCallback((error: Error, recoveryStrategy?: string) => {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    setState(prev => ({
      ...prev,
      error,
      isError: true,
      isRecovering: false,
      lastRetryTime: Date.now(),
      canRetry: prev.retryCount < maxRetries,
      recoveryStrategy: recoveryStrategy || null,
    }));

    // Handle error with error handler
    if (enableAnalytics) {
      errorHandler.handleError(error, {
        ...context,
        errorId,
        retryCount: state.retryCount,
        recoveryStrategy,
      });
    }

    // Track error in performance monitoring
    if (enableAnalytics) {
      performanceMonitor.trackError(error, {
        ...context,
        errorId,
        retryCount: state.retryCount,
        recoveryStrategy,
      });
    }

    // Show user notification
    if (enableUserNotification && state.retryCount === 0) {
      showError(getErrorMessage(error), {
        action: state.canRetry ? {
          label: 'Retry',
          onPress: () => retry(),
        } : undefined,
      });
    }
  }, [maxRetries, context, enableAnalytics, enableUserNotification, showError, state.retryCount, state.canRetry]);

  // Retry operation
  const retry = useCallback(async () => {
    if (state.retryCount >= maxRetries) {
      return;
    }

    setState(prev => ({
      ...prev,
      isRecovering: true,
      retryCount: prev.retryCount + 1,
    }));

    try {
      const delay = getRetryDelay(state.retryCount);
      
      if (delay > 0) {
        await new Promise(resolve => {
          retryTimeoutRef.current = setTimeout(resolve, delay);
        });
      }

      await asyncOperation();

      // Success - clear error state
      setState(prev => ({
        ...prev,
        error: null,
        isError: false,
        isRecovering: false,
        recoveryStrategy: null,
      }));

      if (enableUserNotification && state.retryCount > 0) {
        showSuccess('Operation completed successfully');
      }

      if (enableAnalytics) {
        performanceMonitor.trackUserInteraction('error_recovery_success', 0, {
          retryCount: state.retryCount + 1,
          recoveryStrategy: state.recoveryStrategy,
        });
      }

    } catch (error) {
      setState(prev => ({
        ...prev,
        isRecovering: false,
        canRetry: prev.retryCount < maxRetries,
      }));

      if (state.retryCount + 1 >= maxRetries) {
        if (enableUserNotification) {
          showError('Maximum retry attempts reached. Please try again later.');
        }
      }

      throw error;
    }
  }, [
    state.retryCount,
    state.recoveryStrategy,
    maxRetries,
    getRetryDelay,
    asyncOperation,
    enableUserNotification,
    enableAnalytics,
    showError,
    showSuccess,
  ]);

  // Force retry (ignores retry count)
  const forceRetry = useCallback(async () => {
    setState(prev => ({
      ...prev,
      isRecovering: true,
      retryCount: 0,
      canRetry: true,
    }));

    try {
      await asyncOperation();
      
      setState(prev => ({
        ...prev,
        error: null,
        isError: false,
        isRecovering: false,
        recoveryStrategy: null,
      }));

      if (enableUserNotification) {
        showSuccess('Operation completed successfully');
      }

    } catch (error) {
      setState(prev => ({
        ...prev,
        isRecovering: false,
        error: error as Error,
        isError: true,
      }));
      throw error;
    }
  }, [asyncOperation, enableUserNotification, showSuccess]);

  // Clear error state
  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      isError: false,
      isRecovering: false,
      retryCount: 0,
      canRetry: true,
      recoveryStrategy: null,
    }));
  }, []);

  // Set recovery strategy
  const setRecoveryStrategy = useCallback((strategy: string) => {
    setState(prev => ({
      ...prev,
      recoveryStrategy: strategy,
    }));
  }, []);

  // Utility functions
  const getErrorMessage = useCallback((error?: Error): string => {
    const err = error || state.error;
    if (!err) return '';
    
    if (err.message.includes('Network')) {
      return 'Network connection issue. Please check your internet connection.';
    }
    if (err.message.includes('timeout')) {
      return 'Request timed out. Please try again.';
    }
    if (err.message.includes('401') || err.message.includes('Unauthorized')) {
      return 'Authentication required. Please log in again.';
    }
    if (err.message.includes('403') || err.message.includes('Forbidden')) {
      return 'Access denied. You don\'t have permission for this action.';
    }
    if (err.message.includes('404') || err.message.includes('Not Found')) {
      return 'The requested resource was not found.';
    }
    if (err.message.includes('500') || err.message.includes('Server Error')) {
      return 'Server error. Please try again later.';
    }
    
    return err.message || 'An unexpected error occurred.';
  }, [state.error]);

  const isNetworkError = useCallback((): boolean => {
    return state.error?.message.toLowerCase().includes('network') || false;
  }, [state.error]);

  const isServerError = useCallback((): boolean => {
    return state.error?.message.includes('5') || false;
  }, [state.error]);

  const isAuthError = useCallback((): boolean => {
    return state.error?.message.includes('401') || 
           state.error?.message.includes('403') || 
           state.error?.message.toLowerCase().includes('unauthorized') || false;
  }, [state.error]);

  const getRecoveryRecommendation = useCallback((): string => {
    if (isNetworkError()) {
      return 'Check your internet connection and try again.';
    }
    if (isAuthError()) {
      return 'Please log in again to continue.';
    }
    if (isServerError()) {
      return 'Server is experiencing issues. Please try again in a few minutes.';
    }
    if (state.retryCount >= maxRetries) {
      return 'Maximum retry attempts reached. Please restart the app or contact support.';
    }
    return 'Please try again or contact support if the problem persists.';
  }, [isNetworkError, isAuthError, isServerError, state.retryCount, maxRetries]);

  // Auto-retry on network restore
  useEffect(() => {
    if (!autoRetryOnNetworkRestore || !state.isError || !isNetworkError()) {
      return;
    }

    const unsubscribe = NetInfo.addEventListener(networkState => {
      if (networkState.isConnected && state.canRetry) {
        retry();
      }
    });

    return () => unsubscribe();
  }, [autoRetryOnNetworkRestore, state.isError, state.canRetry, isNetworkError, retry]);

  // Auto-retry on app focus
  useEffect(() => {
    if (!autoRetryOnAppFocus || !state.isError) {
      return;
    }

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        if (state.canRetry && Date.now() - (state.lastRetryTime || 0) > 5000) {
          retry();
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [autoRetryOnAppFocus, state.isError, state.canRetry, state.lastRetryTime, retry]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (networkListenerRef.current) {
        networkListenerRef.current();
      }
    };
  }, []);

  return {
    ...state,
    handleError,
    retry,
    clearError,
    forceRetry,
    setRecoveryStrategy,
    getErrorMessage,
    isNetworkError,
    isServerError,
    isAuthError,
    getRecoveryRecommendation,
  };
};
