/**
 * Enhanced Data Synchronization Service - Offline-First Data Management
 *
 * Service Contract:
 * - Implements offline-first data synchronization with conflict resolution
 * - Manages data consistency across offline/online states
 * - Provides intelligent sync strategies and background synchronization
 * - Handles data versioning and conflict detection
 * - Implements optimistic updates with rollback capabilities
 * - Ensures data integrity and consistency across all operations
 *
 * @version 3.0.0 - Enhanced with Comprehensive Offline-First Architecture
 * <AUTHOR> Development Team
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from 'events';
import { offlineManager } from './offlineManager';
import cacheService from './cacheService';

// Sync configuration
export const SYNC_CONFIG = {
  SYNC_INTERVAL: 30000, // 30 seconds
  CONFLICT_RESOLUTION_STRATEGY: 'client_wins', // 'client_wins' | 'server_wins' | 'merge'
  MAX_SYNC_RETRIES: 3,
  BATCH_SIZE: 50,
  DATA_VERSION_KEY: 'data_version',
  SYNC_STATE_KEY: 'sync_state',
} as const;

// Data entity types
export interface DataEntity {
  id: string;
  type: string;
  data: any;
  version: number;
  lastModified: number;
  isDeleted?: boolean;
  isDirty?: boolean;
  conflictResolution?: 'pending' | 'resolved';
}

// Sync state
export interface SyncState {
  lastSyncTimestamp: number;
  pendingChanges: number;
  conflictCount: number;
  syncInProgress: boolean;
  lastSyncError?: string;
}

// Conflict resolution
export interface DataConflict {
  id: string;
  type: string;
  clientVersion: DataEntity;
  serverVersion: DataEntity;
  conflictType: 'update_conflict' | 'delete_conflict' | 'create_conflict';
  timestamp: number;
}

// Sync events
export type DataSyncEvents = {
  'sync-started': [];
  'sync-completed': [{ synced: number; conflicts: number }];
  'sync-failed': [Error];
  'conflict-detected': [DataConflict];
  'conflict-resolved': [DataConflict];
  'data-updated': [DataEntity];
  'data-deleted': [string];
};

export class DataSyncService extends EventEmitter {
  private static instance: DataSyncService;
  private syncTimer: NodeJS.Timeout | null = null;
  private syncInProgress = false;
  private pendingChanges = new Map<string, DataEntity>();
  private conflicts = new Map<string, DataConflict>();

  private constructor() {
    super();
    this.initializeSyncTimer();
    this.setupOfflineManagerListeners();
  }

  static getInstance(): DataSyncService {
    if (!DataSyncService.instance) {
      DataSyncService.instance = new DataSyncService();
    }
    return DataSyncService.instance;
  }

  /**
   * Initialize periodic sync timer
   */
  private initializeSyncTimer(): void {
    this.syncTimer = setInterval(() => {
      if (offlineManager.isOnline() && !this.syncInProgress) {
        this.syncData();
      }
    }, SYNC_CONFIG.SYNC_INTERVAL);
  }

  /**
   * Setup offline manager event listeners
   */
  private setupOfflineManagerListeners(): void {
    offlineManager.on('network-change', (networkState) => {
      if (networkState.isConnected && !this.syncInProgress) {
        // Trigger immediate sync when coming back online
        setTimeout(() => this.syncData(), 1000);
      }
    });
  }

  /**
   * Save data entity with optimistic updates
   */
  async saveEntity(entity: Omit<DataEntity, 'version' | 'lastModified' | 'isDirty'>): Promise<DataEntity> {
    const now = Date.now();
    const existingEntity = await this.getEntity(entity.id, entity.type);
    
    const dataEntity: DataEntity = {
      ...entity,
      version: existingEntity ? existingEntity.version + 1 : 1,
      lastModified: now,
      isDirty: true,
    };

    // Save to local storage immediately (optimistic update)
    await this.saveEntityLocally(dataEntity);
    
    // Add to pending changes for sync
    this.pendingChanges.set(`${entity.type}_${entity.id}`, dataEntity);
    
    // Cache the entity
    await cacheService.set(`entity_${entity.type}_${entity.id}`, dataEntity, 24 * 60 * 60 * 1000);
    
    this.emit('data-updated', dataEntity);

    // Try to sync immediately if online
    if (offlineManager.isOnline()) {
      this.syncEntity(dataEntity);
    }

    return dataEntity;
  }

  /**
   * Get data entity with fallback to cache and local storage
   */
  async getEntity(id: string, type: string): Promise<DataEntity | null> {
    const cacheKey = `entity_${type}_${id}`;
    
    // Try cache first
    let entity = await cacheService.get<DataEntity>(cacheKey);
    if (entity) {
      return entity;
    }

    // Try local storage
    entity = await this.getEntityLocally(id, type);
    if (entity) {
      // Cache for future use
      await cacheService.set(cacheKey, entity, 24 * 60 * 60 * 1000);
      return entity;
    }

    // Try to fetch from server if online
    if (offlineManager.isOnline()) {
      try {
        entity = await this.fetchEntityFromServer(id, type);
        if (entity) {
          await this.saveEntityLocally(entity);
          await cacheService.set(cacheKey, entity, 24 * 60 * 60 * 1000);
          return entity;
        }
      } catch (error) {
        console.error(`Failed to fetch entity ${type}:${id} from server:`, error);
      }
    }

    return null;
  }

  /**
   * Delete data entity with soft delete
   */
  async deleteEntity(id: string, type: string): Promise<void> {
    const entity = await this.getEntity(id, type);
    if (!entity) {
      throw new Error(`Entity ${type}:${id} not found`);
    }

    const deletedEntity: DataEntity = {
      ...entity,
      isDeleted: true,
      isDirty: true,
      lastModified: Date.now(),
      version: entity.version + 1,
    };

    // Save deletion marker locally
    await this.saveEntityLocally(deletedEntity);
    
    // Add to pending changes
    this.pendingChanges.set(`${type}_${id}`, deletedEntity);
    
    // Remove from cache
    await cacheService.delete(`entity_${type}_${id}`);
    
    this.emit('data-deleted', id);

    // Try to sync immediately if online
    if (offlineManager.isOnline()) {
      this.syncEntity(deletedEntity);
    }
  }

  /**
   * Sync all pending changes with server
   */
  async syncData(): Promise<{ synced: number; conflicts: number }> {
    if (this.syncInProgress || !offlineManager.isOnline()) {
      return { synced: 0, conflicts: 0 };
    }

    this.syncInProgress = true;
    this.emit('sync-started');

    let syncedCount = 0;
    let conflictCount = 0;

    try {
      // Get all pending changes
      const pendingEntities = Array.from(this.pendingChanges.values());
      
      // Process in batches
      for (let i = 0; i < pendingEntities.length; i += SYNC_CONFIG.BATCH_SIZE) {
        const batch = pendingEntities.slice(i, i + SYNC_CONFIG.BATCH_SIZE);
        
        for (const entity of batch) {
          try {
            const result = await this.syncEntity(entity);
            if (result.success) {
              syncedCount++;
              this.pendingChanges.delete(`${entity.type}_${entity.id}`);
            } else if (result.conflict) {
              conflictCount++;
              this.conflicts.set(`${entity.type}_${entity.id}`, result.conflict);
              this.emit('conflict-detected', result.conflict);
            }
          } catch (error) {
            console.error(`Failed to sync entity ${entity.type}:${entity.id}:`, error);
          }
        }
      }

      // Update sync state
      await this.updateSyncState({
        lastSyncTimestamp: Date.now(),
        pendingChanges: this.pendingChanges.size,
        conflictCount: this.conflicts.size,
        syncInProgress: false,
      });

      this.emit('sync-completed', { synced: syncedCount, conflicts: conflictCount });
      return { synced: syncedCount, conflicts: conflictCount };

    } catch (error) {
      console.error('Data sync failed:', error);
      this.emit('sync-failed', error as Error);
      return { synced: syncedCount, conflicts: conflictCount };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync individual entity with conflict detection
   */
  private async syncEntity(entity: DataEntity): Promise<{
    success: boolean;
    conflict?: DataConflict;
  }> {
    try {
      // Fetch current server version
      const serverEntity = await this.fetchEntityFromServer(entity.id, entity.type);
      
      if (serverEntity && serverEntity.version > entity.version) {
        // Conflict detected
        const conflict: DataConflict = {
          id: entity.id,
          type: entity.type,
          clientVersion: entity,
          serverVersion: serverEntity,
          conflictType: entity.isDeleted ? 'delete_conflict' : 'update_conflict',
          timestamp: Date.now(),
        };

        return { success: false, conflict };
      }

      // No conflict, proceed with sync
      if (entity.isDeleted) {
        await this.deleteEntityOnServer(entity.id, entity.type);
      } else {
        await this.saveEntityToServer(entity);
      }

      // Mark as clean
      entity.isDirty = false;
      await this.saveEntityLocally(entity);

      return { success: true };

    } catch (error) {
      console.error(`Failed to sync entity ${entity.type}:${entity.id}:`, error);
      throw error;
    }
  }

  /**
   * Resolve data conflict
   */
  async resolveConflict(
    conflictId: string,
    resolution: 'client_wins' | 'server_wins' | 'merge',
    mergedData?: any
  ): Promise<void> {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict) {
      throw new Error(`Conflict ${conflictId} not found`);
    }

    let resolvedEntity: DataEntity;

    switch (resolution) {
      case 'client_wins':
        resolvedEntity = conflict.clientVersion;
        break;
      case 'server_wins':
        resolvedEntity = conflict.serverVersion;
        break;
      case 'merge':
        if (!mergedData) {
          throw new Error('Merged data required for merge resolution');
        }
        resolvedEntity = {
          ...conflict.clientVersion,
          data: mergedData,
          version: Math.max(conflict.clientVersion.version, conflict.serverVersion.version) + 1,
          lastModified: Date.now(),
        };
        break;
    }

    // Save resolved entity
    await this.saveEntity(resolvedEntity);
    
    // Remove from conflicts
    this.conflicts.delete(conflictId);
    
    this.emit('conflict-resolved', conflict);
  }

  /**
   * Get current sync state
   */
  async getSyncState(): Promise<SyncState> {
    try {
      const stateData = await AsyncStorage.getItem(SYNC_CONFIG.SYNC_STATE_KEY);
      return stateData ? JSON.parse(stateData) : {
        lastSyncTimestamp: 0,
        pendingChanges: this.pendingChanges.size,
        conflictCount: this.conflicts.size,
        syncInProgress: this.syncInProgress,
      };
    } catch (error) {
      console.error('Failed to get sync state:', error);
      return {
        lastSyncTimestamp: 0,
        pendingChanges: 0,
        conflictCount: 0,
        syncInProgress: false,
      };
    }
  }

  /**
   * Force sync all data
   */
  async forceSyncAll(): Promise<{ synced: number; conflicts: number }> {
    if (!offlineManager.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    return await this.syncData();
  }

  /**
   * Clear all local data and conflicts
   */
  async clearAllData(): Promise<void> {
    this.pendingChanges.clear();
    this.conflicts.clear();
    
    await Promise.all([
      AsyncStorage.removeItem(SYNC_CONFIG.SYNC_STATE_KEY),
      cacheService.clear(),
    ]);
  }

  // Private helper methods
  private async saveEntityLocally(entity: DataEntity): Promise<void> {
    const key = `local_entity_${entity.type}_${entity.id}`;
    await AsyncStorage.setItem(key, JSON.stringify(entity));
  }

  private async getEntityLocally(id: string, type: string): Promise<DataEntity | null> {
    try {
      const key = `local_entity_${type}_${id}`;
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`Failed to get local entity ${type}:${id}:`, error);
      return null;
    }
  }

  private async fetchEntityFromServer(id: string, type: string): Promise<DataEntity | null> {
    // This would be implemented with actual API calls
    // For now, return null as placeholder
    return null;
  }

  private async saveEntityToServer(entity: DataEntity): Promise<void> {
    // This would be implemented with actual API calls
    // For now, just log as placeholder
    console.log('Saving entity to server:', entity);
  }

  private async deleteEntityOnServer(id: string, type: string): Promise<void> {
    // This would be implemented with actual API calls
    // For now, just log as placeholder
    console.log('Deleting entity on server:', type, id);
  }

  private async updateSyncState(state: Partial<SyncState>): Promise<void> {
    try {
      const currentState = await this.getSyncState();
      const newState = { ...currentState, ...state };
      await AsyncStorage.setItem(SYNC_CONFIG.SYNC_STATE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.error('Failed to update sync state:', error);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.removeAllListeners();
  }
}

// Export singleton instance
export const dataSyncService = DataSyncService.getInstance();
