/**
 * Enhanced Error Handling Service
 * Centralized error handling with comprehensive recovery and user feedback
 */

import { Alert } from 'react-native';
import { errorHandler } from '../utils/errorHandler';
import { performanceMonitor } from './performanceMonitor';

export interface ErrorHandlingConfig {
  enableUserFeedback: boolean;
  enableAnalytics: boolean;
  enableRecovery: boolean;
  enableOfflineStorage: boolean;
  maxRetries: number;
  retryDelay: number;
  userFeedbackThreshold: 'low' | 'medium' | 'high' | 'critical';
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: number;
  additionalData?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  error: Error;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'network' | 'validation' | 'authentication' | 'server' | 'client' | 'unknown';
  userMessage: string;
  technicalMessage: string;
  recoveryStrategy?: string;
  timestamp: number;
}

export interface RecoveryStrategy {
  name: string;
  description: string;
  execute: () => Promise<boolean>;
  conditions: (error: Error, context: ErrorContext) => boolean;
  priority: number;
}

class EnhancedErrorHandlingService {
  private config: ErrorHandlingConfig;
  private errorQueue: ErrorReport[] = [];
  private recoveryStrategies: RecoveryStrategy[] = [];
  private errorListeners: ((error: ErrorReport) => void)[] = [];

  constructor(config: Partial<ErrorHandlingConfig> = {}) {
    this.config = {
      enableUserFeedback: true,
      enableAnalytics: true,
      enableRecovery: true,
      enableOfflineStorage: false,
      maxRetries: 3,
      retryDelay: 2000,
      userFeedbackThreshold: 'medium',
      ...config,
    };

    this.initializeRecoveryStrategies();
  }

  private initializeRecoveryStrategies() {
    // Network recovery strategy
    this.addRecoveryStrategy({
      name: 'network_retry',
      description: 'Retry network requests with exponential backoff',
      execute: async () => {
        // Wait for network connectivity
        await new Promise(resolve => setTimeout(resolve, 1000));
        return true;
      },
      conditions: (error) => error.message.toLowerCase().includes('network'),
      priority: 1,
    });

    // Authentication recovery strategy
    this.addRecoveryStrategy({
      name: 'auth_refresh',
      description: 'Refresh authentication tokens',
      execute: async () => {
        try {
          // Implement token refresh logic here
          return true;
        } catch {
          return false;
        }
      },
      conditions: (error) => 
        error.message.includes('401') || 
        error.message.toLowerCase().includes('unauthorized'),
      priority: 2,
    });

    // Cache recovery strategy
    this.addRecoveryStrategy({
      name: 'cache_fallback',
      description: 'Fall back to cached data',
      execute: async () => {
        // Implement cache fallback logic here
        return true;
      },
      conditions: (error, context) => 
        error.message.toLowerCase().includes('server') && 
        context.action?.includes('fetch'),
      priority: 3,
    });
  }

  public addRecoveryStrategy(strategy: RecoveryStrategy) {
    this.recoveryStrategies.push(strategy);
    this.recoveryStrategies.sort((a, b) => a.priority - b.priority);
  }

  public addErrorListener(listener: (error: ErrorReport) => void) {
    this.errorListeners.push(listener);
  }

  public removeErrorListener(listener: (error: ErrorReport) => void) {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  private notifyErrorListeners(errorReport: ErrorReport) {
    this.errorListeners.forEach(listener => {
      try {
        listener(errorReport);
      } catch (error) {
        console.error('Error in error listener:', error);
      }
    });
  }

  private categorizeError(error: Error): ErrorReport['category'] {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'validation';
    }
    if (message.includes('401') || message.includes('403') || message.includes('unauthorized')) {
      return 'authentication';
    }
    if (message.includes('500') || message.includes('server')) {
      return 'server';
    }
    if (message.includes('client') || message.includes('400')) {
      return 'client';
    }
    
    return 'unknown';
  }

  private determineSeverity(error: Error, context: ErrorContext): ErrorReport['severity'] {
    const category = this.categorizeError(error);
    
    // Critical errors
    if (category === 'authentication' && context.component === 'App') {
      return 'critical';
    }
    if (error.message.includes('crash') || error.message.includes('fatal')) {
      return 'critical';
    }
    
    // High severity errors
    if (category === 'server' || category === 'network') {
      return 'high';
    }
    
    // Medium severity errors
    if (category === 'validation' || category === 'client') {
      return 'medium';
    }
    
    // Low severity errors
    return 'low';
  }

  private generateUserMessage(error: Error, category: ErrorReport['category']): string {
    switch (category) {
      case 'network':
        return 'Network connection issue. Please check your internet connection and try again.';
      case 'validation':
        return 'Please check your input and try again.';
      case 'authentication':
        return 'Authentication required. Please log in again.';
      case 'server':
        return 'Server is temporarily unavailable. Please try again later.';
      case 'client':
        return 'Invalid request. Please try again or contact support.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  private async findRecoveryStrategy(error: Error, context: ErrorContext): Promise<RecoveryStrategy | null> {
    for (const strategy of this.recoveryStrategies) {
      if (strategy.conditions(error, context)) {
        return strategy;
      }
    }
    return null;
  }

  public async handleError(
    error: Error,
    context: ErrorContext = {},
    userMessage?: string
  ): Promise<ErrorReport> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error, context);
    
    const errorReport: ErrorReport = {
      id: errorId,
      error,
      context: {
        ...context,
        timestamp: Date.now(),
      },
      severity,
      category,
      userMessage: userMessage || this.generateUserMessage(error, category),
      technicalMessage: error.message,
      timestamp: Date.now(),
    };

    // Find recovery strategy
    if (this.config.enableRecovery) {
      const strategy = await this.findRecoveryStrategy(error, context);
      if (strategy) {
        errorReport.recoveryStrategy = strategy.name;
      }
    }

    // Add to error queue
    this.errorQueue.push(errorReport);
    if (this.errorQueue.length > 100) {
      this.errorQueue.shift(); // Remove oldest error
    }

    // Handle with existing error handler
    if (this.config.enableAnalytics) {
      errorHandler.handleError(error, context);
    }

    // Track in performance monitoring
    if (this.config.enableAnalytics) {
      performanceMonitor.trackError(error, {
        ...context,
        errorId,
        severity,
        category,
      });
    }

    // Notify listeners
    this.notifyErrorListeners(errorReport);

    // Show user feedback if appropriate
    if (this.config.enableUserFeedback && this.shouldShowUserFeedback(severity)) {
      this.showUserFeedback(errorReport);
    }

    return errorReport;
  }

  private shouldShowUserFeedback(severity: ErrorReport['severity']): boolean {
    const thresholds = {
      low: ['medium', 'high', 'critical'],
      medium: ['high', 'critical'],
      high: ['critical'],
      critical: [],
    };
    
    return thresholds[this.config.userFeedbackThreshold].includes(severity) || 
           severity === this.config.userFeedbackThreshold;
  }

  private showUserFeedback(errorReport: ErrorReport) {
    const { severity, userMessage, recoveryStrategy } = errorReport;
    
    const alertTitle = severity === 'critical' ? 'Critical Error' : 
                     severity === 'high' ? 'Error' : 
                     severity === 'medium' ? 'Warning' : 'Notice';

    const buttons = [
      { text: 'OK', style: 'default' as const },
    ];

    if (recoveryStrategy) {
      buttons.unshift({
        text: 'Retry',
        style: 'default' as const,
        onPress: () => this.executeRecovery(errorReport),
      });
    }

    if (severity === 'critical') {
      buttons.push({
        text: 'Report Issue',
        style: 'default' as const,
        onPress: () => this.reportIssue(errorReport),
      });
    }

    Alert.alert(alertTitle, userMessage, buttons);
  }

  private async executeRecovery(errorReport: ErrorReport): Promise<boolean> {
    if (!errorReport.recoveryStrategy) return false;

    const strategy = this.recoveryStrategies.find(s => s.name === errorReport.recoveryStrategy);
    if (!strategy) return false;

    try {
      const success = await strategy.execute();
      
      if (this.config.enableAnalytics) {
        performanceMonitor.trackUserInteraction('error_recovery_attempt', 0, {
          errorId: errorReport.id,
          strategy: strategy.name,
          success,
        });
      }

      return success;
    } catch (recoveryError) {
      console.error('Recovery strategy failed:', recoveryError);
      return false;
    }
  }

  private reportIssue(errorReport: ErrorReport) {
    // Implement issue reporting logic here
    console.log('Reporting issue:', errorReport);
    
    if (this.config.enableAnalytics) {
      performanceMonitor.trackUserInteraction('error_report_submitted', 0, {
        errorId: errorReport.id,
        severity: errorReport.severity,
        category: errorReport.category,
      });
    }
  }

  public getErrorHistory(): ErrorReport[] {
    return [...this.errorQueue];
  }

  public clearErrorHistory() {
    this.errorQueue = [];
  }

  public getErrorStats() {
    const total = this.errorQueue.length;
    const bySeverity = this.errorQueue.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const byCategory = this.errorQueue.reduce((acc, error) => {
      acc[error.category] = (acc[error.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      bySeverity,
      byCategory,
    };
  }

  public updateConfig(newConfig: Partial<ErrorHandlingConfig>) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Create singleton instance
export const enhancedErrorHandlingService = new EnhancedErrorHandlingService();

// Export convenience functions
export const handleError = (error: Error, context?: ErrorContext, userMessage?: string) =>
  enhancedErrorHandlingService.handleError(error, context, userMessage);

export const addRecoveryStrategy = (strategy: RecoveryStrategy) =>
  enhancedErrorHandlingService.addRecoveryStrategy(strategy);

export const addErrorListener = (listener: (error: ErrorReport) => void) =>
  enhancedErrorHandlingService.addErrorListener(listener);
