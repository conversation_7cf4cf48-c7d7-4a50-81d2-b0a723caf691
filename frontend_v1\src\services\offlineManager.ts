/**
 * Enhanced Offline Manager - Comprehensive Offline Support with Intelligent Caching
 *
 * Service Contract:
 * - Manages offline functionality and data synchronization
 * - Implements intelligent caching strategies with cache invalidation
 * - Handles offline queue management for pending operations
 * - Provides seamless online/offline transitions
 * - Implements background sync and conflict resolution
 * - Ensures data consistency and integrity across offline/online states
 *
 * @version 3.0.0 - Enhanced with Comprehensive Offline Support
 * <AUTHOR> Development Team
 */

import NetInfo, { NetInfoState } from '@react-native-async-storage/async-storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from 'events';

// Offline configuration
export const OFFLINE_CONFIG = {
  CACHE_EXPIRY_TIME: 24 * 60 * 60 * 1000, // 24 hours
  MAX_CACHE_SIZE: 50 * 1024 * 1024, // 50MB
  SYNC_RETRY_ATTEMPTS: 3,
  SYNC_RETRY_DELAY: 5000, // 5 seconds
  OFFLINE_QUEUE_KEY: 'offline_queue',
  CACHE_METADATA_KEY: 'cache_metadata',
  LAST_SYNC_KEY: 'last_sync_timestamp',
} as const;

// Offline operation types
export interface OfflineOperation {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  headers?: Record<string, string>;
  timestamp: number;
  retryCount: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

// Cache entry structure
export interface CacheEntry {
  key: string;
  data: any;
  timestamp: number;
  expiryTime: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
  tags: string[];
}

// Cache metadata
export interface CacheMetadata {
  totalSize: number;
  entryCount: number;
  lastCleanup: number;
  version: string;
}

// Network state
export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
  isWifiEnabled: boolean;
}

// Offline manager events
export type OfflineManagerEvents = {
  'network-change': [NetworkState];
  'sync-start': [];
  'sync-complete': [{ success: number; failed: number }];
  'sync-error': [Error];
  'cache-updated': [string];
  'cache-cleared': [];
  'operation-queued': [OfflineOperation];
  'operation-synced': [OfflineOperation];
};

export class OfflineManager extends EventEmitter {
  private static instance: OfflineManager;
  private networkState: NetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown',
    isWifiEnabled: false,
  };
  private syncInProgress = false;
  private syncTimer: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.initializeNetworkListener();
    this.initializePeriodicSync();
  }

  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  /**
   * Initialize network state listener
   */
  private initializeNetworkListener(): void {
    NetInfo.addEventListener((state: NetInfoState) => {
      const newNetworkState: NetworkState = {
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable ?? false,
        type: state.type,
        isWifiEnabled: state.type === 'wifi',
      };

      const wasOffline = !this.networkState.isConnected;
      const isNowOnline = newNetworkState.isConnected;

      this.networkState = newNetworkState;
      this.emit('network-change', newNetworkState);

      // Trigger sync when coming back online
      if (wasOffline && isNowOnline) {
        this.syncOfflineOperations();
      }
    });
  }

  /**
   * Initialize periodic sync for background operations
   */
  private initializePeriodicSync(): void {
    this.syncTimer = setInterval(() => {
      if (this.networkState.isConnected && !this.syncInProgress) {
        this.syncOfflineOperations();
      }
    }, 60000); // Sync every minute when online
  }

  /**
   * Get current network state
   */
  getNetworkState(): NetworkState {
    return { ...this.networkState };
  }

  /**
   * Check if device is online
   */
  isOnline(): boolean {
    return this.networkState.isConnected && this.networkState.isInternetReachable;
  }

  /**
   * Queue operation for offline execution
   */
  async queueOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    const offlineOperation: OfflineOperation = {
      ...operation,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0,
    };

    try {
      const queue = await this.getOfflineQueue();
      queue.push(offlineOperation);
      await this.saveOfflineQueue(queue);
      
      this.emit('operation-queued', offlineOperation);

      // Try to sync immediately if online
      if (this.isOnline()) {
        this.syncOfflineOperations();
      }
    } catch (error) {
      console.error('Failed to queue offline operation:', error);
      throw error;
    }
  }

  /**
   * Sync all queued offline operations
   */
  async syncOfflineOperations(): Promise<{ success: number; failed: number }> {
    if (this.syncInProgress || !this.isOnline()) {
      return { success: 0, failed: 0 };
    }

    this.syncInProgress = true;
    this.emit('sync-start');

    let successCount = 0;
    let failedCount = 0;

    try {
      const queue = await this.getOfflineQueue();
      const sortedQueue = queue.sort((a, b) => {
        // Sort by priority and timestamp
        const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
      });

      const remainingOperations: OfflineOperation[] = [];

      for (const operation of sortedQueue) {
        try {
          await this.executeOperation(operation);
          successCount++;
          this.emit('operation-synced', operation);
        } catch (error) {
          console.error(`Failed to sync operation ${operation.id}:`, error);
          
          operation.retryCount++;
          if (operation.retryCount < OFFLINE_CONFIG.SYNC_RETRY_ATTEMPTS) {
            remainingOperations.push(operation);
          } else {
            failedCount++;
            console.error(`Operation ${operation.id} exceeded retry limit`);
          }
        }
      }

      await this.saveOfflineQueue(remainingOperations);
      await this.updateLastSyncTimestamp();

      this.emit('sync-complete', { success: successCount, failed: failedCount });
      return { success: successCount, failed: failedCount };

    } catch (error) {
      console.error('Sync failed:', error);
      this.emit('sync-error', error as Error);
      return { success: successCount, failed: failedCount };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Execute a single offline operation
   */
  private async executeOperation(operation: OfflineOperation): Promise<any> {
    const { endpoint, method, data, headers } = operation;

    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    if (data && method !== 'GET') {
      requestOptions.body = JSON.stringify(data);
    }

    const response = await fetch(endpoint, requestOptions);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Get offline operations queue
   */
  private async getOfflineQueue(): Promise<OfflineOperation[]> {
    try {
      const queueData = await AsyncStorage.getItem(OFFLINE_CONFIG.OFFLINE_QUEUE_KEY);
      return queueData ? JSON.parse(queueData) : [];
    } catch (error) {
      console.error('Failed to get offline queue:', error);
      return [];
    }
  }

  /**
   * Save offline operations queue
   */
  private async saveOfflineQueue(queue: OfflineOperation[]): Promise<void> {
    try {
      await AsyncStorage.setItem(OFFLINE_CONFIG.OFFLINE_QUEUE_KEY, JSON.stringify(queue));
    } catch (error) {
      console.error('Failed to save offline queue:', error);
      throw error;
    }
  }

  /**
   * Update last sync timestamp
   */
  private async updateLastSyncTimestamp(): Promise<void> {
    try {
      await AsyncStorage.setItem(OFFLINE_CONFIG.LAST_SYNC_KEY, Date.now().toString());
    } catch (error) {
      console.error('Failed to update last sync timestamp:', error);
    }
  }

  /**
   * Get last sync timestamp
   */
  async getLastSyncTimestamp(): Promise<number> {
    try {
      const timestamp = await AsyncStorage.getItem(OFFLINE_CONFIG.LAST_SYNC_KEY);
      return timestamp ? parseInt(timestamp, 10) : 0;
    } catch (error) {
      console.error('Failed to get last sync timestamp:', error);
      return 0;
    }
  }

  /**
   * Clear all offline data
   */
  async clearOfflineData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(OFFLINE_CONFIG.OFFLINE_QUEUE_KEY),
        AsyncStorage.removeItem(OFFLINE_CONFIG.LAST_SYNC_KEY),
      ]);
      
      this.emit('cache-cleared');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      throw error;
    }
  }

  /**
   * Get offline queue status
   */
  async getOfflineStatus(): Promise<{
    queueLength: number;
    lastSync: number;
    isOnline: boolean;
    syncInProgress: boolean;
  }> {
    const queue = await this.getOfflineQueue();
    const lastSync = await this.getLastSyncTimestamp();

    return {
      queueLength: queue.length,
      lastSync,
      isOnline: this.isOnline(),
      syncInProgress: this.syncInProgress,
    };
  }

  /**
   * Force sync all operations
   */
  async forceSyncAll(): Promise<{ success: number; failed: number }> {
    if (!this.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    return await this.syncOfflineOperations();
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.removeAllListeners();
  }
}

// Export singleton instance
export const offlineManager = OfflineManager.getInstance();
