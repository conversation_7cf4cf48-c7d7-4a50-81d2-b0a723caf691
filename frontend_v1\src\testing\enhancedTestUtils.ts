/**
 * Enhanced Test Utilities
 * Provides comprehensive testing utilities for the rebuilt screens and components
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '../contexts/ThemeContext';
import { ToastProvider } from '../components/ui/Toast';
import { EnhancedErrorBoundary } from '../components/error/EnhancedErrorBoundary';
import { AccessibilityComplianceSystem } from '../components/accessibility/AccessibilityComplianceSystem';

// Mock store configuration
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: (state = { user: null, isAuthenticated: false }, action) => state,
      providers: (state = { items: [], loading: false }, action) => state,
      bookings: (state = { items: [], loading: false }, action) => state,
      messages: (state = { conversations: [], loading: false }, action) => state,
      ...initialState,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

// Enhanced render function with all providers
interface EnhancedRenderOptions extends RenderOptions {
  initialState?: any;
  store?: any;
  navigationOptions?: any;
  themeOptions?: any;
  accessibilityOptions?: any;
}

export const renderWithProviders = (
  ui: ReactElement,
  {
    initialState = {},
    store = createMockStore(initialState),
    navigationOptions = {},
    themeOptions = {},
    accessibilityOptions = {},
    ...renderOptions
  }: EnhancedRenderOptions = {}
) => {
  const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
    return (
      <Provider store={store}>
        <NavigationContainer {...navigationOptions}>
          <ThemeProvider {...themeOptions}>
            <AccessibilityComplianceSystem {...accessibilityOptions}>
              <EnhancedErrorBoundary>
                <ToastProvider>
                  {children}
                </ToastProvider>
              </EnhancedErrorBoundary>
            </AccessibilityComplianceSystem>
          </ThemeProvider>
        </NavigationContainer>
      </Provider>
    );
  };

  return render(ui, { wrapper: AllTheProviders, ...renderOptions });
};

// Accessibility testing utilities
export const accessibilityTestUtils = {
  /**
   * Test screen reader accessibility
   */
  testScreenReaderAccessibility: async (component: ReactElement) => {
    const { getAllByRole, getByLabelText } = renderWithProviders(component);
    
    // Check for proper roles
    const buttons = getAllByRole('button');
    const headings = getAllByRole('header');
    const textInputs = getAllByRole('text');
    
    // Verify accessibility labels
    buttons.forEach(button => {
      expect(button.props.accessibilityLabel || button.props.children).toBeTruthy();
    });
    
    headings.forEach(heading => {
      expect(heading.props.accessibilityLabel || heading.props.children).toBeTruthy();
    });
    
    return {
      buttons: buttons.length,
      headings: headings.length,
      textInputs: textInputs.length,
    };
  },

  /**
   * Test keyboard navigation
   */
  testKeyboardNavigation: async (component: ReactElement) => {
    const { getAllByRole } = renderWithProviders(component);
    const focusableElements = getAllByRole('button');
    
    // Test tab navigation
    for (let i = 0; i < focusableElements.length; i++) {
      fireEvent(focusableElements[i], 'focus');
      expect(focusableElements[i]).toHaveFocus();
    }
    
    return focusableElements.length;
  },

  /**
   * Test touch target sizes
   */
  testTouchTargetSizes: (component: ReactElement) => {
    const { getAllByRole } = renderWithProviders(component);
    const touchableElements = getAllByRole('button');
    
    touchableElements.forEach(element => {
      const style = element.props.style || {};
      const minSize = 44;
      
      if (style.width && style.width < minSize) {
        console.warn(`Touch target too small: ${style.width}px width`);
      }
      if (style.height && style.height < minSize) {
        console.warn(`Touch target too small: ${style.height}px height`);
      }
    });
    
    return touchableElements.length;
  },
};

// Performance testing utilities
export const performanceTestUtils = {
  /**
   * Measure component render time
   */
  measureRenderTime: async (component: ReactElement) => {
    const startTime = performance.now();
    renderWithProviders(component);
    const endTime = performance.now();
    return endTime - startTime;
  },

  /**
   * Test component re-render performance
   */
  testReRenderPerformance: async (component: ReactElement, updates: any[]) => {
    const { rerender } = renderWithProviders(component);
    const renderTimes: number[] = [];
    
    for (const update of updates) {
      const startTime = performance.now();
      rerender(React.cloneElement(component, update));
      const endTime = performance.now();
      renderTimes.push(endTime - startTime);
    }
    
    return {
      averageRenderTime: renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length,
      maxRenderTime: Math.max(...renderTimes),
      minRenderTime: Math.min(...renderTimes),
      renderTimes,
    };
  },

  /**
   * Test memory usage
   */
  testMemoryUsage: async (component: ReactElement) => {
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
    const { unmount } = renderWithProviders(component);
    const afterRenderMemory = (performance as any).memory?.usedJSHeapSize || 0;
    unmount();
    const afterUnmountMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    return {
      renderMemoryIncrease: afterRenderMemory - initialMemory,
      memoryLeakage: afterUnmountMemory - initialMemory,
    };
  },
};

// User interaction testing utilities
export const userInteractionTestUtils = {
  /**
   * Simulate user journey
   */
  simulateUserJourney: async (steps: Array<{
    action: 'press' | 'type' | 'scroll' | 'wait';
    target?: string;
    value?: string;
    duration?: number;
  }>) => {
    for (const step of steps) {
      switch (step.action) {
        case 'press':
          if (step.target) {
            const element = document.querySelector(`[data-testid="${step.target}"]`);
            if (element) {
              fireEvent.press(element);
            }
          }
          break;
        case 'type':
          if (step.target && step.value) {
            const element = document.querySelector(`[data-testid="${step.target}"]`);
            if (element) {
              fireEvent.changeText(element, step.value);
            }
          }
          break;
        case 'wait':
          await waitFor(() => {}, { timeout: step.duration || 1000 });
          break;
      }
    }
  },

  /**
   * Test form interactions
   */
  testFormInteractions: async (component: ReactElement, formData: Record<string, string>) => {
    const { getByTestId, getByRole } = renderWithProviders(component);
    
    // Fill form fields
    for (const [fieldName, value] of Object.entries(formData)) {
      const field = getByTestId(fieldName);
      fireEvent.changeText(field, value);
      expect(field.props.value).toBe(value);
    }
    
    // Submit form
    const submitButton = getByRole('button');
    fireEvent.press(submitButton);
    
    return true;
  },

  /**
   * Test navigation interactions
   */
  testNavigationInteractions: async (component: ReactElement, navigationTargets: string[]) => {
    const { getByTestId } = renderWithProviders(component);
    
    for (const target of navigationTargets) {
      const navElement = getByTestId(target);
      fireEvent.press(navElement);
      await waitFor(() => {
        // Verify navigation occurred
        expect(navElement).toBeTruthy();
      });
    }
    
    return navigationTargets.length;
  },
};

// API testing utilities
export const apiTestUtils = {
  /**
   * Mock API responses
   */
  mockApiResponse: (endpoint: string, response: any, delay = 0) => {
    return jest.fn().mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve(response), delay))
    );
  },

  /**
   * Mock API error
   */
  mockApiError: (endpoint: string, error: any, delay = 0) => {
    return jest.fn().mockImplementation(() =>
      new Promise((_, reject) => setTimeout(() => reject(error), delay))
    );
  },

  /**
   * Test loading states
   */
  testLoadingStates: async (component: ReactElement, apiCall: () => Promise<any>) => {
    const { getByTestId, queryByTestId } = renderWithProviders(component);
    
    // Start API call
    const apiPromise = apiCall();
    
    // Check loading state
    await waitFor(() => {
      expect(queryByTestId('loading-indicator')).toBeTruthy();
    });
    
    // Wait for completion
    await apiPromise;
    
    // Check loaded state
    await waitFor(() => {
      expect(queryByTestId('loading-indicator')).toBeFalsy();
    });
    
    return true;
  },

  /**
   * Test error states
   */
  testErrorStates: async (component: ReactElement, errorApiCall: () => Promise<any>) => {
    const { getByTestId, queryByTestId } = renderWithProviders(component);
    
    try {
      await errorApiCall();
    } catch (error) {
      // Expected error
    }
    
    // Check error state
    await waitFor(() => {
      expect(queryByTestId('error-display')).toBeTruthy();
    });
    
    return true;
  },
};

// Screen testing utilities
export const screenTestUtils = {
  /**
   * Test screen rendering
   */
  testScreenRendering: (screen: ReactElement) => {
    const { getByTestId } = renderWithProviders(screen);
    expect(getByTestId('screen-container')).toBeTruthy();
    return true;
  },

  /**
   * Test screen navigation
   */
  testScreenNavigation: async (screen: ReactElement, navigationProps: any) => {
    const mockNavigation = {
      navigate: jest.fn(),
      goBack: jest.fn(),
      ...navigationProps,
    };
    
    const ScreenWithNavigation = () => 
      React.cloneElement(screen, { navigation: mockNavigation });
    
    renderWithProviders(<ScreenWithNavigation />);
    
    return mockNavigation;
  },

  /**
   * Test screen data loading
   */
  testScreenDataLoading: async (screen: ReactElement, mockData: any) => {
    const { getByTestId, queryByTestId } = renderWithProviders(screen);
    
    // Check initial loading state
    expect(queryByTestId('loading-indicator')).toBeTruthy();
    
    // Wait for data to load
    await waitFor(() => {
      expect(queryByTestId('loading-indicator')).toBeFalsy();
      expect(getByTestId('screen-content')).toBeTruthy();
    });
    
    return true;
  },
};

// Test data factories
export const testDataFactories = {
  user: (overrides = {}) => ({
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    phone: '+**********',
    avatar: 'https://example.com/avatar.jpg',
    ...overrides,
  }),

  provider: (overrides = {}) => ({
    id: '1',
    name: 'Test Provider',
    description: 'Test provider description',
    rating: 4.5,
    reviewCount: 100,
    services: ['Service 1', 'Service 2'],
    location: 'Test Location',
    ...overrides,
  }),

  booking: (overrides = {}) => ({
    id: '1',
    providerId: '1',
    userId: '1',
    serviceId: '1',
    date: new Date().toISOString(),
    status: 'confirmed',
    price: 100,
    ...overrides,
  }),

  message: (overrides = {}) => ({
    id: '1',
    senderId: '1',
    receiverId: '2',
    content: 'Test message',
    timestamp: new Date().toISOString(),
    read: false,
    ...overrides,
  }),

  service: (overrides = {}) => ({
    id: '1',
    name: 'Test Service',
    description: 'Test service description',
    price: 50,
    duration: 60,
    category: 'Test Category',
    ...overrides,
  }),
};

// Async testing utilities
export const asyncTestUtils = {
  /**
   * Wait for element to appear
   */
  waitForElement: async (getByTestId: any, testId: string, timeout = 5000) => {
    return waitFor(() => getByTestId(testId), { timeout });
  },

  /**
   * Wait for element to disappear
   */
  waitForElementToDisappear: async (queryByTestId: any, testId: string, timeout = 5000) => {
    return waitFor(() => expect(queryByTestId(testId)).toBeFalsy(), { timeout });
  },

  /**
   * Wait for async operation
   */
  waitForAsyncOperation: async (operation: () => Promise<any>, timeout = 5000) => {
    return act(async () => {
      await Promise.race([
        operation(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Operation timeout')), timeout)
        ),
      ]);
    });
  },
};

// Export all utilities
export {
  renderWithProviders as render,
  createMockStore,
  fireEvent,
  waitFor,
  act,
};
