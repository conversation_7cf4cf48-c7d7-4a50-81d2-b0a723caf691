/**
 * Integration Test Suite
 * Comprehensive integration tests for the rebuilt application
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { CustomerHomeScreen } from '../screens/CustomerHomeScreen';
import { SearchScreen } from '../screens/SearchScreen';
import { ProviderDetailsScreen } from '../screens/ProviderDetailsScreen';
import { BookingScreen } from '../screens/BookingScreen';
import { ThemeProvider } from '../contexts/ThemeContext';
import { ToastProvider } from '../components/ui/Toast';
import { EnhancedErrorBoundary } from '../components/error/EnhancedErrorBoundary';
import { AccessibilityComplianceSystem } from '../components/accessibility/AccessibilityComplianceSystem';
import {
  renderWithProviders,
  testDataFactories,
  userInteractionTestUtils,
  performanceTestUtils,
  accessibilityTestUtils,
} from './enhancedTestUtils';

// Mock services
jest.mock('../services/customerService');
jest.mock('../services/providerService');
jest.mock('../services/bookingService');
jest.mock('../services/searchService');

describe('Integration Tests', () => {
  let mockStore: any;
  let mockNavigation: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockStore = configureStore({
      reducer: {
        auth: (state = { user: testDataFactories.user(), isAuthenticated: true }, action) => state,
        providers: (state = { items: [], loading: false }, action) => state,
        bookings: (state = { items: [], loading: false }, action) => state,
        search: (state = { results: [], loading: false }, action) => state,
      },
    });

    mockNavigation = {
      navigate: jest.fn(),
      goBack: jest.fn(),
      setOptions: jest.fn(),
      addListener: jest.fn(() => () => {}),
    };
  });

  describe('Complete User Journey', () => {
    it('should handle complete booking journey', async () => {
      // Step 1: Start from home screen
      const { getByTestId, getByText } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      // Verify home screen loads
      expect(getByText('Featured Providers')).toBeTruthy();

      // Step 2: Navigate to search
      const searchButton = getByTestId('search-button');
      fireEvent.press(searchButton);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Search');

      // Step 3: Simulate search screen
      const searchScreen = renderWithProviders(
        <SearchScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      // Perform search
      const searchInput = searchScreen.getByTestId('search-input');
      fireEvent.changeText(searchInput, 'hair salon');

      const searchSubmitButton = searchScreen.getByTestId('search-submit');
      fireEvent.press(searchSubmitButton);

      // Step 4: Select provider from results
      await waitFor(() => {
        const providerCard = searchScreen.getByTestId('provider-card-1');
        fireEvent.press(providerCard);
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith('ProviderDetails', {
        providerId: '1',
      });

      // Step 5: Simulate provider details screen
      const providerScreen = renderWithProviders(
        <ProviderDetailsScreen navigation={mockNavigation} route={{ params: { providerId: '1' } }} />,
        { store: mockStore }
      );

      // Book service
      const bookButton = providerScreen.getByTestId('book-service-button');
      fireEvent.press(bookButton);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Booking', {
        providerId: '1',
        serviceId: expect.any(String),
      });

      // Step 6: Complete booking
      const bookingScreen = renderWithProviders(
        <BookingScreen navigation={mockNavigation} route={{ params: { providerId: '1' } }} />,
        { store: mockStore }
      );

      const confirmButton = bookingScreen.getByTestId('confirm-booking-button');
      fireEvent.press(confirmButton);

      // Verify booking completion
      await waitFor(() => {
        expect(bookingScreen.getByText('Booking Confirmed')).toBeTruthy();
      });
    });

    it('should handle user authentication flow', async () => {
      // Start with unauthenticated state
      const unauthenticatedStore = configureStore({
        reducer: {
          auth: (state = { user: null, isAuthenticated: false }, action) => state,
          providers: (state = { items: [], loading: false }, action) => state,
        },
      });

      const { getByTestId } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: unauthenticatedStore }
      );

      // Try to access authenticated feature
      const bookingButton = getByTestId('quick-booking-button');
      fireEvent.press(bookingButton);

      // Should navigate to login
      expect(mockNavigation.navigate).toHaveBeenCalledWith('Login');
    });

    it('should handle error recovery flow', async () => {
      // Mock API error
      const errorStore = configureStore({
        reducer: {
          auth: (state = { user: testDataFactories.user(), isAuthenticated: true }, action) => state,
          providers: (state = { items: [], loading: false, error: new Error('Network error') }, action) => state,
        },
      });

      const { getByTestId } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: errorStore }
      );

      // Verify error display
      expect(getByTestId('customer-home-global-error')).toBeTruthy();

      // Test retry functionality
      const retryButton = getByTestId('customer-home-global-error-action');
      fireEvent.press(retryButton);

      // Verify retry attempt
      expect(retryButton).toBeTruthy();
    });
  });

  describe('Cross-Screen Navigation', () => {
    it('should maintain state across navigation', async () => {
      const { getByTestId } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      // Navigate to search with category
      const categoryCard = getByTestId('category-card-1');
      fireEvent.press(categoryCard);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Search', {
        categoryId: '1',
      });

      // Verify state is maintained
      expect(mockStore.getState().auth.isAuthenticated).toBe(true);
    });

    it('should handle deep linking correctly', async () => {
      const deepLinkRoute = {
        params: {
          screen: 'ProviderDetails',
          params: { providerId: '123' },
        },
      };

      const { getByTestId } = renderWithProviders(
        <ProviderDetailsScreen navigation={mockNavigation} route={deepLinkRoute} />,
        { store: mockStore }
      );

      // Verify deep link handling
      expect(getByTestId('provider-details-container')).toBeTruthy();
    });
  });

  describe('Performance Integration', () => {
    it('should maintain performance across screen transitions', async () => {
      const startTime = performance.now();

      // Simulate rapid navigation
      const screens = [
        <CustomerHomeScreen navigation={mockNavigation} />,
        <SearchScreen navigation={mockNavigation} />,
        <ProviderDetailsScreen navigation={mockNavigation} route={{ params: { providerId: '1' } }} />,
      ];

      for (const screen of screens) {
        const { unmount } = renderWithProviders(screen, { store: mockStore });
        unmount();
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      expect(totalTime).toBeLessThan(3000); // 3 seconds for all screens
    });

    it('should handle memory management across screens', async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Render and unmount multiple screens
      for (let i = 0; i < 5; i++) {
        const { unmount } = renderWithProviders(
          <CustomerHomeScreen navigation={mockNavigation} />,
          { store: mockStore }
        );
        unmount();
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      expect(memoryIncrease).toBeLessThan(10000000); // 10MB limit
    });
  });

  describe('Accessibility Integration', () => {
    it('should maintain accessibility across all screens', async () => {
      const screens = [
        <CustomerHomeScreen navigation={mockNavigation} />,
        <SearchScreen navigation={mockNavigation} />,
        <ProviderDetailsScreen navigation={mockNavigation} route={{ params: { providerId: '1' } }} />,
      ];

      for (const screen of screens) {
        const accessibilityResults = await accessibilityTestUtils.testScreenReaderAccessibility(screen);
        
        expect(accessibilityResults.buttons).toBeGreaterThan(0);
        expect(accessibilityResults.headings).toBeGreaterThan(0);
      }
    });

    it('should support keyboard navigation across screens', async () => {
      const screens = [
        <CustomerHomeScreen navigation={mockNavigation} />,
        <SearchScreen navigation={mockNavigation} />,
      ];

      for (const screen of screens) {
        const focusableElements = await accessibilityTestUtils.testKeyboardNavigation(screen);
        expect(focusableElements).toBeGreaterThan(0);
      }
    });
  });

  describe('Error Boundary Integration', () => {
    it('should handle errors gracefully across the application', async () => {
      const ErrorComponent = () => {
        throw new Error('Test error');
      };

      const { getByText } = renderWithProviders(
        <EnhancedErrorBoundary>
          <ErrorComponent />
        </EnhancedErrorBoundary>,
        { store: mockStore }
      );

      expect(getByText('Something went wrong')).toBeTruthy();
    });

    it('should isolate errors to specific components', async () => {
      const ErrorComponent = () => {
        throw new Error('Component error');
      };

      const WorkingComponent = () => <div>Working component</div>;

      const { getByText } = renderWithProviders(
        <div>
          <EnhancedErrorBoundary>
            <ErrorComponent />
          </EnhancedErrorBoundary>
          <WorkingComponent />
        </div>,
        { store: mockStore }
      );

      expect(getByText('Something went wrong')).toBeTruthy();
      expect(getByText('Working component')).toBeTruthy();
    });
  });

  describe('Real-time Features Integration', () => {
    it('should handle real-time updates correctly', async () => {
      const { getByTestId } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      // Simulate real-time update
      act(() => {
        mockStore.dispatch({
          type: 'providers/updateProvider',
          payload: testDataFactories.provider({ id: '1', name: 'Updated Provider' }),
        });
      });

      await waitFor(() => {
        expect(getByTestId('provider-card-1')).toBeTruthy();
      });
    });

    it('should handle offline/online transitions', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false,
      });

      const { getByTestId } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      expect(getByTestId('offline-indicator')).toBeTruthy();

      // Mock online state
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: true,
      });

      // Trigger online event
      window.dispatchEvent(new Event('online'));

      await waitFor(() => {
        expect(() => getByTestId('offline-indicator')).toThrow();
      });
    });
  });

  describe('Data Synchronization', () => {
    it('should synchronize data across components', async () => {
      const { getByTestId, rerender } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      // Update store data
      act(() => {
        mockStore.dispatch({
          type: 'providers/setProviders',
          payload: [testDataFactories.provider({ id: '1', name: 'New Provider' })],
        });
      });

      // Re-render with updated data
      rerender(<CustomerHomeScreen navigation={mockNavigation} />);

      await waitFor(() => {
        expect(getByTestId('provider-card-1')).toBeTruthy();
      });
    });

    it('should handle concurrent data updates', async () => {
      const { getByTestId } = renderWithProviders(
        <CustomerHomeScreen navigation={mockNavigation} />,
        { store: mockStore }
      );

      // Simulate concurrent updates
      const updates = [
        { type: 'providers/setProviders', payload: [testDataFactories.provider()] },
        { type: 'bookings/setBookings', payload: [testDataFactories.booking()] },
        { type: 'auth/setUser', payload: testDataFactories.user() },
      ];

      act(() => {
        updates.forEach(update => mockStore.dispatch(update));
      });

      await waitFor(() => {
        expect(getByTestId('customer-home-scroll')).toBeTruthy();
      });
    });
  });
});
