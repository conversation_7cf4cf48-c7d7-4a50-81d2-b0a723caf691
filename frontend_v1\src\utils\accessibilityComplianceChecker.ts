/**
 * Accessibility Compliance Checker
 * Provides runtime checking for WCAG 2.1 AA compliance
 */

import { Dimensions, Platform } from 'react-native';
import { AccessibilityUtils } from './accessibilityUtils';

export interface ComplianceIssue {
  id: string;
  criterion: string;
  level: 'A' | 'AA' | 'AAA';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  element?: string;
  location?: string;
  autoFixable?: boolean;
}

export interface ComplianceReport {
  timestamp: number;
  totalIssues: number;
  criticalIssues: number;
  highIssues: number;
  mediumIssues: number;
  lowIssues: number;
  complianceScore: number; // 0-100
  issues: ComplianceIssue[];
  passedCriteria: string[];
}

export class AccessibilityComplianceChecker {
  private static instance: AccessibilityComplianceChecker;
  private issues: ComplianceIssue[] = [];
  private checkedElements: Set<string> = new Set();

  static getInstance(): AccessibilityComplianceChecker {
    if (!AccessibilityComplianceChecker.instance) {
      AccessibilityComplianceChecker.instance = new AccessibilityComplianceChecker();
    }
    return AccessibilityComplianceChecker.instance;
  }

  // WCAG 2.1 AA Criteria Checkers

  /**
   * 1.1.1 Non-text Content (Level A)
   * Check if images have appropriate alt text
   */
  checkNonTextContent(element: any, props: any): ComplianceIssue | null {
    if (element.type === 'Image' || props.source) {
      if (!props.accessibilityLabel && !props.alt) {
        return {
          id: '1.1.1',
          criterion: 'Non-text Content',
          level: 'A',
          severity: 'high',
          description: 'Image missing alternative text',
          recommendation: 'Add accessibilityLabel prop to provide alternative text for images',
          element: element.displayName || 'Image',
          autoFixable: false,
        };
      }
    }
    return null;
  }

  /**
   * 1.3.1 Info and Relationships (Level A)
   * Check for proper semantic structure
   */
  checkInfoAndRelationships(element: any, props: any): ComplianceIssue | null {
    // Check for proper heading hierarchy
    if (props.accessibilityRole === 'header' && !props.accessibilityLevel) {
      return {
        id: '1.3.1',
        criterion: 'Info and Relationships',
        level: 'A',
        severity: 'medium',
        description: 'Header missing accessibility level',
        recommendation: 'Add accessibilityLevel prop to indicate heading hierarchy',
        element: element.displayName || 'Header',
        autoFixable: false,
      };
    }

    // Check for form labels
    if (element.type === 'TextInput' && !props.accessibilityLabel) {
      return {
        id: '1.3.1',
        criterion: 'Info and Relationships',
        level: 'A',
        severity: 'high',
        description: 'Form input missing label',
        recommendation: 'Add accessibilityLabel prop to provide a label for the input',
        element: 'TextInput',
        autoFixable: false,
      };
    }

    return null;
  }

  /**
   * 1.4.3 Contrast (Minimum) (Level AA)
   * Check color contrast ratios
   */
  checkColorContrast(textColor: string, backgroundColor: string, fontSize: number = 16): ComplianceIssue | null {
    const contrastRatio = AccessibilityUtils.getContrastRatio(textColor, backgroundColor);
    const isLargeText = fontSize >= 18 || (fontSize >= 14 && fontSize < 18); // Simplified large text check
    const requiredRatio = isLargeText ? 3.0 : 4.5;

    if (contrastRatio < requiredRatio) {
      return {
        id: '1.4.3',
        criterion: 'Contrast (Minimum)',
        level: 'AA',
        severity: contrastRatio < 3.0 ? 'critical' : 'high',
        description: `Color contrast ratio ${contrastRatio.toFixed(2)}:1 is below required ${requiredRatio}:1`,
        recommendation: `Increase contrast between text and background colors to meet ${requiredRatio}:1 ratio`,
        autoFixable: true,
      };
    }

    return null;
  }

  /**
   * 1.4.11 Non-text Contrast (Level AA)
   * Check UI component contrast
   */
  checkNonTextContrast(componentColor: string, backgroundColor: string): ComplianceIssue | null {
    const contrastRatio = AccessibilityUtils.getContrastRatio(componentColor, backgroundColor);
    const requiredRatio = 3.0;

    if (contrastRatio < requiredRatio) {
      return {
        id: '1.4.11',
        criterion: 'Non-text Contrast',
        level: 'AA',
        severity: 'medium',
        description: `UI component contrast ratio ${contrastRatio.toFixed(2)}:1 is below required ${requiredRatio}:1`,
        recommendation: 'Increase contrast for UI components like borders, icons, and focus indicators',
        autoFixable: true,
      };
    }

    return null;
  }

  /**
   * 2.1.1 Keyboard (Level A)
   * Check keyboard accessibility
   */
  checkKeyboardAccessibility(element: any, props: any): ComplianceIssue | null {
    const interactiveElements = ['TouchableOpacity', 'TouchableHighlight', 'TouchableWithoutFeedback', 'Button'];
    
    if (interactiveElements.includes(element.type) && props.accessible === false) {
      return {
        id: '2.1.1',
        criterion: 'Keyboard',
        level: 'A',
        severity: 'high',
        description: 'Interactive element not accessible via keyboard',
        recommendation: 'Ensure interactive elements are keyboard accessible by setting accessible={true}',
        element: element.type,
        autoFixable: true,
      };
    }

    return null;
  }

  /**
   * 2.4.7 Focus Visible (Level AA)
   * Check focus indicators
   */
  checkFocusVisible(element: any, props: any, style: any): ComplianceIssue | null {
    const interactiveElements = ['TouchableOpacity', 'TouchableHighlight', 'Button'];
    
    if (interactiveElements.includes(element.type)) {
      // Check if focus styles are defined (simplified check)
      const hasFocusStyles = style && (
        style.borderWidth || 
        style.shadowOpacity || 
        style.elevation ||
        (style.borderColor && style.borderColor !== 'transparent')
      );

      if (!hasFocusStyles) {
        return {
          id: '2.4.7',
          criterion: 'Focus Visible',
          level: 'AA',
          severity: 'medium',
          description: 'Interactive element lacks visible focus indicator',
          recommendation: 'Add visible focus styles like border, shadow, or background color change',
          element: element.type,
          autoFixable: true,
        };
      }
    }

    return null;
  }

  /**
   * 2.5.8 Target Size (Minimum) (Level AA)
   * Check touch target sizes
   */
  checkTargetSize(element: any, style: any): ComplianceIssue | null {
    const interactiveElements = ['TouchableOpacity', 'TouchableHighlight', 'TouchableWithoutFeedback', 'Button'];
    
    if (interactiveElements.includes(element.type)) {
      const minSize = AccessibilityUtils.WCAG_STANDARDS.TOUCH_TARGETS.MINIMUM_SIZE;
      const width = style?.width || style?.minWidth || 0;
      const height = style?.height || style?.minHeight || 0;

      if ((width > 0 && width < minSize) || (height > 0 && height < minSize)) {
        return {
          id: '2.5.8',
          criterion: 'Target Size (Minimum)',
          level: 'AA',
          severity: 'medium',
          description: `Touch target size ${width}x${height}px is below minimum ${minSize}x${minSize}px`,
          recommendation: `Increase touch target size to at least ${minSize}x${minSize}px`,
          element: element.type,
          autoFixable: true,
        };
      }
    }

    return null;
  }

  /**
   * 3.3.2 Labels or Instructions (Level A)
   * Check form labels and instructions
   */
  checkLabelsOrInstructions(element: any, props: any): ComplianceIssue | null {
    if (element.type === 'TextInput') {
      if (!props.accessibilityLabel && !props.placeholder) {
        return {
          id: '3.3.2',
          criterion: 'Labels or Instructions',
          level: 'A',
          severity: 'high',
          description: 'Form input lacks label or instructions',
          recommendation: 'Add accessibilityLabel or placeholder to provide input instructions',
          element: 'TextInput',
          autoFixable: false,
        };
      }
    }

    return null;
  }

  /**
   * 4.1.2 Name, Role, Value (Level A)
   * Check accessibility properties
   */
  checkNameRoleValue(element: any, props: any): ComplianceIssue | null {
    const interactiveElements = ['TouchableOpacity', 'TouchableHighlight', 'Button', 'TextInput'];
    
    if (interactiveElements.includes(element.type)) {
      if (!props.accessibilityLabel && !props.accessibilityRole) {
        return {
          id: '4.1.2',
          criterion: 'Name, Role, Value',
          level: 'A',
          severity: 'high',
          description: 'Interactive element missing name and role',
          recommendation: 'Add accessibilityLabel and accessibilityRole props',
          element: element.type,
          autoFixable: false,
        };
      }
    }

    return null;
  }

  /**
   * Check a single element for compliance issues
   */
  checkElement(element: any, props: any = {}, style: any = {}): ComplianceIssue[] {
    const issues: ComplianceIssue[] = [];
    const elementId = props.testID || element.type || 'unknown';

    // Skip if already checked
    if (this.checkedElements.has(elementId)) {
      return [];
    }

    this.checkedElements.add(elementId);

    // Run all checks
    const checks = [
      this.checkNonTextContent(element, props),
      this.checkInfoAndRelationships(element, props),
      this.checkKeyboardAccessibility(element, props),
      this.checkFocusVisible(element, props, style),
      this.checkTargetSize(element, style),
      this.checkLabelsOrInstructions(element, props),
      this.checkNameRoleValue(element, props),
    ];

    // Color contrast checks (if colors are provided)
    if (style.color && style.backgroundColor) {
      checks.push(this.checkColorContrast(style.color, style.backgroundColor, style.fontSize));
    }

    if (style.borderColor && style.backgroundColor) {
      checks.push(this.checkNonTextContrast(style.borderColor, style.backgroundColor));
    }

    // Filter out null results
    issues.push(...checks.filter(issue => issue !== null) as ComplianceIssue[]);

    return issues;
  }

  /**
   * Generate a compliance report
   */
  generateReport(): ComplianceReport {
    const criticalIssues = this.issues.filter(issue => issue.severity === 'critical').length;
    const highIssues = this.issues.filter(issue => issue.severity === 'high').length;
    const mediumIssues = this.issues.filter(issue => issue.severity === 'medium').length;
    const lowIssues = this.issues.filter(issue => issue.severity === 'low').length;

    // Calculate compliance score (simplified)
    const totalPossibleIssues = this.checkedElements.size * 9; // 9 criteria checked
    const actualIssues = this.issues.length;
    const complianceScore = Math.max(0, Math.round(((totalPossibleIssues - actualIssues) / totalPossibleIssues) * 100));

    // Get passed criteria
    const allCriteria = ['1.1.1', '1.3.1', '1.4.3', '1.4.11', '2.1.1', '2.4.7', '2.5.8', '3.3.2', '4.1.2'];
    const failedCriteria = new Set(this.issues.map(issue => issue.id));
    const passedCriteria = allCriteria.filter(criterion => !failedCriteria.has(criterion));

    return {
      timestamp: Date.now(),
      totalIssues: this.issues.length,
      criticalIssues,
      highIssues,
      mediumIssues,
      lowIssues,
      complianceScore,
      issues: [...this.issues],
      passedCriteria,
    };
  }

  /**
   * Auto-fix issues where possible
   */
  autoFixIssues(): { fixed: number; suggestions: string[] } {
    let fixed = 0;
    const suggestions: string[] = [];

    this.issues.forEach(issue => {
      if (issue.autoFixable) {
        // In a real implementation, this would apply fixes
        suggestions.push(`Auto-fix available for ${issue.criterion}: ${issue.recommendation}`);
        fixed++;
      } else {
        suggestions.push(`Manual fix required for ${issue.criterion}: ${issue.recommendation}`);
      }
    });

    return { fixed, suggestions };
  }

  /**
   * Clear all issues and reset checker
   */
  reset(): void {
    this.issues = [];
    this.checkedElements.clear();
  }

  /**
   * Add issues to the checker
   */
  addIssues(issues: ComplianceIssue[]): void {
    this.issues.push(...issues);
  }

  /**
   * Get current issues
   */
  getIssues(): ComplianceIssue[] {
    return [...this.issues];
  }
}

// Export singleton instance
export const accessibilityComplianceChecker = AccessibilityComplianceChecker.getInstance();

// Utility functions
export const checkElementCompliance = (element: any, props: any = {}, style: any = {}) => {
  return accessibilityComplianceChecker.checkElement(element, props, style);
};

export const generateComplianceReport = () => {
  return accessibilityComplianceChecker.generateReport();
};

export const autoFixAccessibilityIssues = () => {
  return accessibilityComplianceChecker.autoFixIssues();
};
