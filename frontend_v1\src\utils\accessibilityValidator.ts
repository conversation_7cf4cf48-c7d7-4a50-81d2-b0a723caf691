/**
 * Enhanced Accessibility Validator - WCAG 2.1 AA Compliance Testing
 *
 * Utility Contract:
 * - Validates components and screens for WCAG 2.1 AA compliance
 * - Provides automated accessibility testing and reporting
 * - Checks color contrast, touch targets, keyboard navigation
 * - Validates screen reader compatibility and semantic markup
 * - Generates detailed accessibility reports with remediation guidance
 * - Integrates with development workflow for continuous compliance
 *
 * @version 3.0.0 - Enhanced with Comprehensive WCAG 2.1 AA Testing
 * <AUTHOR> Development Team
 */

import { AccessibilityInfo, Dimensions, Platform } from 'react-native';
import { Colors } from '../constants/Colors';

// WCAG 2.1 AA Standards
export const WCAG_STANDARDS = {
  COLOR_CONTRAST: {
    NORMAL_TEXT: 4.5,
    LARGE_TEXT: 3.0,
    NON_TEXT: 3.0,
  },
  TOUCH_TARGET: {
    MIN_SIZE: 44,
    RECOMMENDED_SIZE: 48,
  },
  TIMING: {
    MIN_DURATION: 20000, // 20 seconds minimum for timed content
  },
  MOTION: {
    MAX_ANIMATION_DURATION: 5000, // 5 seconds max for essential animations
  },
} as const;

export interface AccessibilityIssue {
  id: string;
  severity: 'error' | 'warning' | 'info';
  criterion: string;
  description: string;
  element?: string;
  remediation: string;
  wcagReference: string;
}

export interface AccessibilityReport {
  score: number;
  level: 'A' | 'AA' | 'AAA' | 'FAIL';
  issues: AccessibilityIssue[];
  summary: {
    errors: number;
    warnings: number;
    passed: number;
    total: number;
  };
  recommendations: string[];
}

export class AccessibilityValidator {
  private static instance: AccessibilityValidator;
  private issues: AccessibilityIssue[] = [];

  static getInstance(): AccessibilityValidator {
    if (!AccessibilityValidator.instance) {
      AccessibilityValidator.instance = new AccessibilityValidator();
    }
    return AccessibilityValidator.instance;
  }

  /**
   * Validate color contrast according to WCAG 2.1 AA standards
   */
  validateColorContrast(
    foreground: string,
    background: string,
    fontSize: number = 16,
    fontWeight: string = 'normal'
  ): AccessibilityIssue | null {
    const contrast = this.calculateContrastRatio(foreground, background);
    const isLargeText = fontSize >= 18 || (fontSize >= 14 && fontWeight === 'bold');
    const requiredContrast = isLargeText 
      ? WCAG_STANDARDS.COLOR_CONTRAST.LARGE_TEXT 
      : WCAG_STANDARDS.COLOR_CONTRAST.NORMAL_TEXT;

    if (contrast < requiredContrast) {
      return {
        id: `contrast_${Date.now()}`,
        severity: 'error',
        criterion: '1.4.3 Contrast (Minimum)',
        description: `Color contrast ratio ${contrast.toFixed(2)}:1 is below the required ${requiredContrast}:1`,
        remediation: `Increase color contrast to at least ${requiredContrast}:1. Consider using darker text or lighter background.`,
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html',
      };
    }

    return null;
  }

  /**
   * Validate touch target sizes according to WCAG 2.1 AA standards
   */
  validateTouchTarget(width: number, height: number, elementType: string): AccessibilityIssue | null {
    const minSize = WCAG_STANDARDS.TOUCH_TARGET.MIN_SIZE;
    const size = Math.min(width, height);

    if (size < minSize) {
      return {
        id: `touch_target_${Date.now()}`,
        severity: 'error',
        criterion: '2.5.5 Target Size',
        description: `Touch target size ${size}px is below the minimum ${minSize}px`,
        element: elementType,
        remediation: `Increase touch target size to at least ${minSize}x${minSize}px or provide adequate spacing.`,
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/target-size.html',
      };
    }

    return null;
  }

  /**
   * Validate keyboard navigation and focus management
   */
  validateKeyboardNavigation(
    hasTabIndex: boolean,
    isFocusable: boolean,
    hasVisibleFocus: boolean,
    elementType: string
  ): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // Check if interactive elements are keyboard accessible
    if ((elementType === 'button' || elementType === 'link') && !isFocusable) {
      issues.push({
        id: `keyboard_access_${Date.now()}`,
        severity: 'error',
        criterion: '2.1.1 Keyboard',
        description: `Interactive ${elementType} is not keyboard accessible`,
        element: elementType,
        remediation: 'Ensure all interactive elements can be reached and operated using only the keyboard.',
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html',
      });
    }

    // Check for visible focus indicators
    if (isFocusable && !hasVisibleFocus) {
      issues.push({
        id: `focus_indicator_${Date.now()}`,
        severity: 'error',
        criterion: '2.4.7 Focus Visible',
        description: `Focusable ${elementType} lacks visible focus indicator`,
        element: elementType,
        remediation: 'Add clear visual focus indicators that meet contrast requirements.',
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/focus-visible.html',
      });
    }

    return issues;
  }

  /**
   * Validate screen reader compatibility
   */
  validateScreenReaderSupport(
    hasAccessibilityLabel: boolean,
    hasAccessibilityHint: boolean,
    hasAccessibilityRole: boolean,
    elementType: string,
    isInteractive: boolean
  ): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // Check for accessibility labels on interactive elements
    if (isInteractive && !hasAccessibilityLabel) {
      issues.push({
        id: `screen_reader_label_${Date.now()}`,
        severity: 'error',
        criterion: '4.1.2 Name, Role, Value',
        description: `Interactive ${elementType} missing accessibility label`,
        element: elementType,
        remediation: 'Add descriptive accessibility labels to all interactive elements.',
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/name-role-value.html',
      });
    }

    // Check for proper roles
    if (isInteractive && !hasAccessibilityRole) {
      issues.push({
        id: `screen_reader_role_${Date.now()}`,
        severity: 'warning',
        criterion: '4.1.2 Name, Role, Value',
        description: `${elementType} should have explicit accessibility role`,
        element: elementType,
        remediation: 'Add appropriate accessibility roles to help screen readers understand element purpose.',
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/name-role-value.html',
      });
    }

    return issues;
  }

  /**
   * Validate timing and motion for accessibility
   */
  validateTimingAndMotion(
    hasTimeout: boolean,
    timeoutDuration: number,
    hasAnimation: boolean,
    animationDuration: number,
    canPauseAnimation: boolean
  ): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // Check timing constraints
    if (hasTimeout && timeoutDuration < WCAG_STANDARDS.TIMING.MIN_DURATION) {
      issues.push({
        id: `timing_${Date.now()}`,
        severity: 'error',
        criterion: '2.2.1 Timing Adjustable',
        description: `Timeout duration ${timeoutDuration}ms is too short`,
        remediation: 'Extend timeout to at least 20 seconds or provide user controls to adjust timing.',
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/timing-adjustable.html',
      });
    }

    // Check animation controls
    if (hasAnimation && animationDuration > WCAG_STANDARDS.MOTION.MAX_ANIMATION_DURATION && !canPauseAnimation) {
      issues.push({
        id: `motion_${Date.now()}`,
        severity: 'warning',
        criterion: '2.2.2 Pause, Stop, Hide',
        description: `Long animation without pause controls may cause vestibular disorders`,
        remediation: 'Provide controls to pause, stop, or hide animations longer than 5 seconds.',
        wcagReference: 'https://www.w3.org/WAI/WCAG21/Understanding/pause-stop-hide.html',
      });
    }

    return issues;
  }

  /**
   * Generate comprehensive accessibility report
   */
  generateReport(issues: AccessibilityIssue[]): AccessibilityReport {
    const errors = issues.filter(issue => issue.severity === 'error').length;
    const warnings = issues.filter(issue => issue.severity === 'warning').length;
    const total = issues.length;
    const passed = Math.max(0, 100 - total); // Simplified scoring

    let level: 'A' | 'AA' | 'AAA' | 'FAIL' = 'AAA';
    let score = 100;

    if (errors > 0) {
      level = 'FAIL';
      score = Math.max(0, 100 - (errors * 10) - (warnings * 5));
    } else if (warnings > 5) {
      level = 'A';
      score = Math.max(70, 100 - (warnings * 3));
    } else if (warnings > 2) {
      level = 'AA';
      score = Math.max(85, 100 - (warnings * 2));
    }

    const recommendations = this.generateRecommendations(issues);

    return {
      score,
      level,
      issues,
      summary: {
        errors,
        warnings,
        passed,
        total,
      },
      recommendations,
    };
  }

  /**
   * Calculate color contrast ratio
   */
  private calculateContrastRatio(color1: string, color2: string): number {
    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Calculate relative luminance of a color
   */
  private getLuminance(color: string): number {
    // Simplified luminance calculation
    // In a real implementation, you'd parse the color and calculate properly
    const rgb = this.hexToRgb(color);
    if (!rgb) return 0;

    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /**
   * Convert hex color to RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Generate accessibility recommendations
   */
  private generateRecommendations(issues: AccessibilityIssue[]): string[] {
    const recommendations: string[] = [];

    if (issues.some(issue => issue.criterion.includes('Contrast'))) {
      recommendations.push('Review and improve color contrast ratios throughout the application');
    }

    if (issues.some(issue => issue.criterion.includes('Target Size'))) {
      recommendations.push('Increase touch target sizes to meet minimum accessibility requirements');
    }

    if (issues.some(issue => issue.criterion.includes('Keyboard'))) {
      recommendations.push('Ensure all interactive elements are keyboard accessible');
    }

    if (issues.some(issue => issue.criterion.includes('Focus'))) {
      recommendations.push('Add clear visual focus indicators to all focusable elements');
    }

    if (issues.some(issue => issue.criterion.includes('Name, Role, Value'))) {
      recommendations.push('Improve screen reader support with proper labels and roles');
    }

    return recommendations;
  }
}

// Export singleton instance
export const accessibilityValidator = AccessibilityValidator.getInstance();
