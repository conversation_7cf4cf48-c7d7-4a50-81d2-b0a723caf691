/**
 * Enhanced Data Validation Utilities - Comprehensive Input Validation and Sanitization
 *
 * Utility Contract:
 * - Provides comprehensive data validation for all user inputs and API responses
 * - Implements input sanitization to prevent XSS and injection attacks
 * - Validates data types, formats, and business rules
 * - Supports custom validation rules and error messaging
 * - Integrates with form validation and API security measures
 * - Ensures data integrity and security across the application
 *
 * @version 3.0.0 - Enhanced with Comprehensive Security and Validation
 * <AUTHOR> Development Team
 */

import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Common validation patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  PHONE: /^\+?[\d\s\-\(\)]{10,}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  NAME: /^[a-zA-Z\s\-']{2,50}$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
} as const;

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  sanitizedData?: any;
}

// User data validation schemas
export const userValidationSchemas = {
  // Registration validation
  registration: z.object({
    firstName: z.string()
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name must not exceed 50 characters')
      .regex(VALIDATION_PATTERNS.NAME, 'First name contains invalid characters'),
    
    lastName: z.string()
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name must not exceed 50 characters')
      .regex(VALIDATION_PATTERNS.NAME, 'Last name contains invalid characters'),
    
    email: z.string()
      .email('Please enter a valid email address')
      .max(255, 'Email address is too long'),
    
    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(VALIDATION_PATTERNS.PASSWORD, 'Password must contain uppercase, lowercase, number, and special character'),
    
    confirmPassword: z.string(),
    
    phone: z.string()
      .regex(VALIDATION_PATTERNS.PHONE, 'Please enter a valid phone number')
      .optional(),
    
    dateOfBirth: z.date()
      .max(new Date(), 'Date of birth cannot be in the future')
      .refine(date => {
        const age = new Date().getFullYear() - date.getFullYear();
        return age >= 13;
      }, 'You must be at least 13 years old'),
    
    termsAccepted: z.boolean()
      .refine(val => val === true, 'You must accept the terms and conditions'),
  }).refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }),

  // Login validation
  login: z.object({
    email: z.string()
      .email('Please enter a valid email address'),
    
    password: z.string()
      .min(1, 'Password is required'),
    
    rememberMe: z.boolean().optional(),
  }),

  // Profile update validation
  profileUpdate: z.object({
    firstName: z.string()
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name must not exceed 50 characters')
      .regex(VALIDATION_PATTERNS.NAME, 'First name contains invalid characters'),
    
    lastName: z.string()
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name must not exceed 50 characters')
      .regex(VALIDATION_PATTERNS.NAME, 'Last name contains invalid characters'),
    
    phone: z.string()
      .regex(VALIDATION_PATTERNS.PHONE, 'Please enter a valid phone number')
      .optional(),
    
    bio: z.string()
      .max(500, 'Bio must not exceed 500 characters')
      .optional(),
    
    location: z.string()
      .max(100, 'Location must not exceed 100 characters')
      .optional(),
  }),

  // Password change validation
  passwordChange: z.object({
    currentPassword: z.string()
      .min(1, 'Current password is required'),
    
    newPassword: z.string()
      .min(8, 'New password must be at least 8 characters')
      .regex(VALIDATION_PATTERNS.PASSWORD, 'Password must contain uppercase, lowercase, number, and special character'),
    
    confirmNewPassword: z.string(),
  }).refine(data => data.newPassword === data.confirmNewPassword, {
    message: 'New passwords do not match',
    path: ['confirmNewPassword'],
  }).refine(data => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  }),
};

// Booking data validation schemas
export const bookingValidationSchemas = {
  // Booking creation validation
  createBooking: z.object({
    serviceId: z.string()
      .regex(VALIDATION_PATTERNS.UUID, 'Invalid service ID'),
    
    providerId: z.string()
      .regex(VALIDATION_PATTERNS.UUID, 'Invalid provider ID'),
    
    scheduledDate: z.date()
      .min(new Date(), 'Booking date cannot be in the past'),
    
    scheduledTime: z.string()
      .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    
    duration: z.number()
      .min(15, 'Minimum booking duration is 15 minutes')
      .max(480, 'Maximum booking duration is 8 hours'),
    
    notes: z.string()
      .max(1000, 'Notes must not exceed 1000 characters')
      .optional(),
    
    location: z.object({
      address: z.string().min(1, 'Address is required'),
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180),
    }).optional(),
  }),

  // Booking update validation
  updateBooking: z.object({
    scheduledDate: z.date()
      .min(new Date(), 'Booking date cannot be in the past')
      .optional(),
    
    scheduledTime: z.string()
      .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format')
      .optional(),
    
    notes: z.string()
      .max(1000, 'Notes must not exceed 1000 characters')
      .optional(),
  }),
};

// Message validation schemas
export const messageValidationSchemas = {
  // Send message validation
  sendMessage: z.object({
    conversationId: z.string()
      .regex(VALIDATION_PATTERNS.UUID, 'Invalid conversation ID'),
    
    content: z.string()
      .min(1, 'Message cannot be empty')
      .max(2000, 'Message must not exceed 2000 characters'),
    
    messageType: z.enum(['text', 'image', 'file'])
      .default('text'),
    
    attachments: z.array(z.object({
      type: z.enum(['image', 'document']),
      url: z.string().url('Invalid attachment URL'),
      name: z.string().min(1, 'Attachment name is required'),
      size: z.number().max(10 * 1024 * 1024, 'File size must not exceed 10MB'),
    })).optional(),
  }),
};

// Data sanitization utilities
export class DataSanitizer {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  static sanitizeHtml(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: [],
    });
  }

  /**
   * Sanitize text input by removing potentially dangerous characters
   */
  static sanitizeText(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Sanitize URL to ensure it's safe
   */
  static sanitizeUrl(input: string): string {
    const url = input.trim();
    
    // Only allow http and https protocols
    if (!url.match(/^https?:\/\//)) {
      return '';
    }
    
    // Remove javascript: and data: protocols
    if (url.match(/^(javascript|data|vbscript):/i)) {
      return '';
    }
    
    return url;
  }

  /**
   * Sanitize file name to prevent path traversal
   */
  static sanitizeFileName(input: string): string {
    return input
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special characters
      .replace(/\.{2,}/g, '.') // Remove multiple dots
      .replace(/^\.+|\.+$/g, '') // Remove leading/trailing dots
      .substring(0, 255); // Limit length
  }

  /**
   * Sanitize object by applying appropriate sanitization to each field
   */
  static sanitizeObject(obj: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeText(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeObject(value);
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

// Main validation class
export class DataValidator {
  /**
   * Validate data against a Zod schema
   */
  static validate<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult {
    try {
      const result = schema.safeParse(data);
      
      if (result.success) {
        return {
          isValid: true,
          errors: [],
          sanitizedData: DataSanitizer.sanitizeObject(result.data as any),
        };
      } else {
        const errors: ValidationError[] = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
          value: err.path.reduce((obj, key) => obj?.[key], data),
        }));
        
        return {
          isValid: false,
          errors,
        };
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [{
          field: 'general',
          message: 'Validation failed due to an unexpected error',
          code: 'VALIDATION_ERROR',
        }],
      };
    }
  }

  /**
   * Validate user registration data
   */
  static validateRegistration(data: unknown): ValidationResult {
    return this.validate(userValidationSchemas.registration, data);
  }

  /**
   * Validate user login data
   */
  static validateLogin(data: unknown): ValidationResult {
    return this.validate(userValidationSchemas.login, data);
  }

  /**
   * Validate profile update data
   */
  static validateProfileUpdate(data: unknown): ValidationResult {
    return this.validate(userValidationSchemas.profileUpdate, data);
  }

  /**
   * Validate password change data
   */
  static validatePasswordChange(data: unknown): ValidationResult {
    return this.validate(userValidationSchemas.passwordChange, data);
  }

  /**
   * Validate booking creation data
   */
  static validateCreateBooking(data: unknown): ValidationResult {
    return this.validate(bookingValidationSchemas.createBooking, data);
  }

  /**
   * Validate booking update data
   */
  static validateUpdateBooking(data: unknown): ValidationResult {
    return this.validate(bookingValidationSchemas.updateBooking, data);
  }

  /**
   * Validate message sending data
   */
  static validateSendMessage(data: unknown): ValidationResult {
    return this.validate(messageValidationSchemas.sendMessage, data);
  }

  /**
   * Validate API response data structure
   */
  static validateApiResponse(data: unknown, expectedSchema: z.ZodSchema): ValidationResult {
    return this.validate(expectedSchema, data);
  }

  /**
   * Custom validation for business rules
   */
  static validateBusinessRules(data: any, rules: Record<string, (value: any) => boolean | string>): ValidationError[] {
    const errors: ValidationError[] = [];
    
    for (const [field, rule] of Object.entries(rules)) {
      const value = data[field];
      const result = rule(value);
      
      if (typeof result === 'string') {
        errors.push({
          field,
          message: result,
          code: 'BUSINESS_RULE_VIOLATION',
          value,
        });
      } else if (result === false) {
        errors.push({
          field,
          message: `${field} violates business rules`,
          code: 'BUSINESS_RULE_VIOLATION',
          value,
        });
      }
    }
    
    return errors;
  }
}

// Export validation utilities
export { userValidationSchemas, bookingValidationSchemas, messageValidationSchemas };
