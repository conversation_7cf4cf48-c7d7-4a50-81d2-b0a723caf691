/**
 * Enhanced Localization Utilities - Comprehensive Internationalization Support
 *
 * Utility Contract:
 * - Provides comprehensive localization utilities for multi-language support
 * - Handles date/time formatting with locale-specific patterns
 * - Manages number/currency formatting with regional preferences
 * - Supports RTL languages and text direction handling
 * - Implements pluralization and gender-aware translations
 * - Provides accessibility integration with screen readers
 * - Handles cultural adaptations and locale-specific behaviors
 *
 * @version 3.0.0 - Enhanced with Comprehensive Internationalization Features
 * <AUTHOR> Development Team
 */

import { I18nManager, Platform } from 'react-native';
import { format, formatDistance, formatRelative, parseISO } from 'date-fns';
import { enCA, frCA, es, de, it, pt, ja, ko, zh, ar } from 'date-fns/locale';

// Supported locales configuration
export const LOCALE_CONFIG = {
  'en-CA': {
    code: 'en-CA',
    name: 'English (Canada)',
    nativeName: 'English (Canada)',
    flag: '🇨🇦',
    rtl: false,
    dateFnsLocale: enCA,
    currency: 'CAD',
    currencySymbol: '$',
    dateFormat: 'MM/dd/yyyy',
    timeFormat: 'h:mm a',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      grouping: [3],
    },
  },
  'fr-CA': {
    code: 'fr-CA',
    name: 'Français (Canada)',
    nativeName: 'Français (Canada)',
    flag: '🇨🇦',
    rtl: false,
    dateFnsLocale: frCA,
    currency: 'CAD',
    currencySymbol: '$',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
      grouping: [3],
    },
  },
  'es-ES': {
    code: 'es-ES',
    name: 'Español',
    nativeName: 'Español',
    flag: '🇪🇸',
    rtl: false,
    dateFnsLocale: es,
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      grouping: [3],
    },
  },
  'ar-SA': {
    code: 'ar-SA',
    name: 'العربية',
    nativeName: 'العربية',
    flag: '🇸🇦',
    rtl: true,
    dateFnsLocale: ar,
    currency: 'SAR',
    currencySymbol: 'ر.س',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      grouping: [3],
    },
  },
} as const;

export type SupportedLocale = keyof typeof LOCALE_CONFIG;

// Translation namespace types
export interface TranslationNamespaces {
  common: any;
  navigation: any;
  forms: any;
  errors: any;
  booking: any;
  messaging: any;
  profile: any;
  services: any;
  payments: any;
  notifications: any;
}

// Pluralization rules
export interface PluralRules {
  zero?: string;
  one: string;
  two?: string;
  few?: string;
  many?: string;
  other: string;
}

// Gender-aware translation
export interface GenderAwareTranslation {
  masculine: string;
  feminine: string;
  neutral?: string;
}

export class LocalizationUtils {
  private static currentLocale: SupportedLocale = 'en-CA';

  /**
   * Set current locale and update RTL settings
   */
  static setLocale(locale: SupportedLocale): void {
    this.currentLocale = locale;
    const localeConfig = LOCALE_CONFIG[locale];
    
    // Update RTL settings for React Native
    if (Platform.OS !== 'web') {
      I18nManager.allowRTL(localeConfig.rtl);
      I18nManager.forceRTL(localeConfig.rtl);
    }
  }

  /**
   * Get current locale configuration
   */
  static getCurrentLocale(): typeof LOCALE_CONFIG[SupportedLocale] {
    return LOCALE_CONFIG[this.currentLocale];
  }

  /**
   * Check if current locale is RTL
   */
  static isRTL(): boolean {
    return LOCALE_CONFIG[this.currentLocale].rtl;
  }

  /**
   * Format date according to current locale
   */
  static formatDate(
    date: Date | string,
    formatString?: string,
    options?: { relative?: boolean; distance?: boolean }
  ): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const localeConfig = this.getCurrentLocale();
    
    if (options?.relative) {
      return formatRelative(dateObj, new Date(), { locale: localeConfig.dateFnsLocale });
    }
    
    if (options?.distance) {
      return formatDistance(dateObj, new Date(), { 
        addSuffix: true, 
        locale: localeConfig.dateFnsLocale 
      });
    }
    
    const formatStr = formatString || localeConfig.dateFormat;
    return format(dateObj, formatStr, { locale: localeConfig.dateFnsLocale });
  }

  /**
   * Format time according to current locale
   */
  static formatTime(date: Date | string, formatString?: string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const localeConfig = this.getCurrentLocale();
    const formatStr = formatString || localeConfig.timeFormat;
    
    return format(dateObj, formatStr, { locale: localeConfig.dateFnsLocale });
  }

  /**
   * Format number according to current locale
   */
  static formatNumber(
    number: number,
    options?: {
      minimumFractionDigits?: number;
      maximumFractionDigits?: number;
      style?: 'decimal' | 'percent';
    }
  ): string {
    const localeConfig = this.getCurrentLocale();
    
    try {
      return new Intl.NumberFormat(localeConfig.code, {
        minimumFractionDigits: options?.minimumFractionDigits,
        maximumFractionDigits: options?.maximumFractionDigits,
        style: options?.style || 'decimal',
      }).format(number);
    } catch (error) {
      // Fallback for environments without Intl support
      return this.formatNumberFallback(number, localeConfig.numberFormat);
    }
  }

  /**
   * Format currency according to current locale
   */
  static formatCurrency(
    amount: number,
    currency?: string,
    options?: {
      minimumFractionDigits?: number;
      maximumFractionDigits?: number;
      showSymbol?: boolean;
    }
  ): string {
    const localeConfig = this.getCurrentLocale();
    const currencyCode = currency || localeConfig.currency;
    
    try {
      return new Intl.NumberFormat(localeConfig.code, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: options?.minimumFractionDigits ?? 2,
        maximumFractionDigits: options?.maximumFractionDigits ?? 2,
      }).format(amount);
    } catch (error) {
      // Fallback for environments without Intl support
      const formattedNumber = this.formatNumberFallback(amount, localeConfig.numberFormat);
      const symbol = options?.showSymbol !== false ? localeConfig.currencySymbol : '';
      return `${symbol}${formattedNumber}`;
    }
  }

  /**
   * Handle pluralization based on count and locale rules
   */
  static pluralize(
    count: number,
    translations: PluralRules,
    locale?: SupportedLocale
  ): string {
    const currentLocale = locale || this.currentLocale;
    
    // Simplified pluralization rules - in a real app, you'd use a proper pluralization library
    if (count === 0 && translations.zero) {
      return translations.zero;
    } else if (count === 1) {
      return translations.one;
    } else if (count === 2 && translations.two) {
      return translations.two;
    } else if (count > 2 && count < 5 && translations.few) {
      return translations.few;
    } else if (count >= 5 && translations.many) {
      return translations.many;
    } else {
      return translations.other;
    }
  }

  /**
   * Handle gender-aware translations
   */
  static genderAware(
    gender: 'masculine' | 'feminine' | 'neutral',
    translations: GenderAwareTranslation
  ): string {
    switch (gender) {
      case 'masculine':
        return translations.masculine;
      case 'feminine':
        return translations.feminine;
      case 'neutral':
        return translations.neutral || translations.masculine;
      default:
        return translations.masculine;
    }
  }

  /**
   * Get text direction for current locale
   */
  static getTextDirection(): 'ltr' | 'rtl' {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  /**
   * Get layout direction styles for current locale
   */
  static getLayoutDirection(): {
    flexDirection: 'row' | 'row-reverse';
    textAlign: 'left' | 'right';
    writingDirection: 'ltr' | 'rtl';
  } {
    const isRTL = this.isRTL();
    
    return {
      flexDirection: isRTL ? 'row-reverse' : 'row',
      textAlign: isRTL ? 'right' : 'left',
      writingDirection: isRTL ? 'rtl' : 'ltr',
    };
  }

  /**
   * Validate translation keys for completeness
   */
  static validateTranslations(
    baseTranslations: Record<string, any>,
    targetTranslations: Record<string, any>
  ): {
    missing: string[];
    extra: string[];
    isComplete: boolean;
  } {
    const missing: string[] = [];
    const extra: string[] = [];
    
    const checkKeys = (base: any, target: any, prefix = '') => {
      for (const key in base) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (!(key in target)) {
          missing.push(fullKey);
        } else if (typeof base[key] === 'object' && typeof target[key] === 'object') {
          checkKeys(base[key], target[key], fullKey);
        }
      }
      
      for (const key in target) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (!(key in base)) {
          extra.push(fullKey);
        }
      }
    };
    
    checkKeys(baseTranslations, targetTranslations);
    
    return {
      missing,
      extra,
      isComplete: missing.length === 0,
    };
  }

  /**
   * Get accessibility announcements for screen readers
   */
  static getAccessibilityAnnouncement(
    key: string,
    params?: Record<string, any>
  ): string {
    // This would integrate with the i18n system to get localized accessibility announcements
    // For now, return a placeholder
    return `Accessibility announcement for ${key}`;
  }

  /**
   * Fallback number formatting for environments without Intl support
   */
  private static formatNumberFallback(
    number: number,
    format: { decimal: string; thousands: string; grouping: number[] }
  ): string {
    const parts = number.toString().split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1] || '';
    
    // Add thousands separators
    const formattedInteger = integerPart.replace(
      /\B(?=(\d{3})+(?!\d))/g,
      format.thousands
    );
    
    if (decimalPart) {
      return `${formattedInteger}${format.decimal}${decimalPart}`;
    }
    
    return formattedInteger;
  }

  /**
   * Get locale-specific keyboard type for inputs
   */
  static getKeyboardType(inputType: 'number' | 'decimal' | 'phone' | 'email'): string {
    const localeConfig = this.getCurrentLocale();
    
    switch (inputType) {
      case 'decimal':
        // Use comma for decimal in some locales
        return localeConfig.numberFormat.decimal === ',' ? 'numeric' : 'decimal-pad';
      case 'number':
        return 'number-pad';
      case 'phone':
        return 'phone-pad';
      case 'email':
        return 'email-address';
      default:
        return 'default';
    }
  }

  /**
   * Get culturally appropriate greeting based on time and locale
   */
  static getTimeBasedGreeting(): string {
    const hour = new Date().getHours();
    const locale = this.currentLocale;
    
    // This would be properly localized in a real implementation
    if (hour < 12) {
      return locale === 'fr-CA' ? 'Bonjour' : 'Good morning';
    } else if (hour < 18) {
      return locale === 'fr-CA' ? 'Bon après-midi' : 'Good afternoon';
    } else {
      return locale === 'fr-CA' ? 'Bonsoir' : 'Good evening';
    }
  }
}

// Export utility functions
export const {
  setLocale,
  getCurrentLocale,
  isRTL,
  formatDate,
  formatTime,
  formatNumber,
  formatCurrency,
  pluralize,
  genderAware,
  getTextDirection,
  getLayoutDirection,
  validateTranslations,
  getAccessibilityAnnouncement,
  getKeyboardType,
  getTimeBasedGreeting,
} = LocalizationUtils;
